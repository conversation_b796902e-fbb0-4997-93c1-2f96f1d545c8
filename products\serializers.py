from rest_framework import serializers
from .models import ProductCategory, Product, ProductImage, ProductVariant, ProductReview
from stores.models import Store


class ProductCategorySerializer(serializers.ModelSerializer):
    children = serializers.SerializerMethodField()
    
    class Meta:
        model = ProductCategory
        fields = ['id', 'name', 'slug', 'description', 'image', 'parent', 'children', 'is_active']
    
    def get_children(self, obj):
        if obj.children.exists():
            return ProductCategorySerializer(obj.children.filter(is_active=True), many=True).data
        return []


class ProductImageSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProductImage
        fields = ['id', 'image', 'alt_text', 'is_primary', 'sort_order']


class ProductVariantSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProductVariant
        fields = ['id', 'name', 'value', 'price_adjustment', 'stock_quantity', 'sku', 'final_price', 'is_active']


class StoreBasicSerializer(serializers.ModelSerializer):
    class Meta:
        model = Store
        fields = ['id', 'name', 'slug', 'logo', 'rating', 'is_active']


class ProductListSerializer(serializers.ModelSerializer):
    store = StoreBasicSerializer(read_only=True)
    category = ProductCategorySerializer(read_only=True)
    primary_image = serializers.SerializerMethodField()
    
    class Meta:
        model = Product
        fields = [
            'id', 'name', 'slug', 'short_description', 'price', 'compare_price', 
            'discount_percentage', 'rating', 'is_in_stock', 'store', 'category', 
            'primary_image', 'is_featured'
        ]
    
    def get_primary_image(self, obj):
        primary_image = obj.images.filter(is_primary=True).first()
        if primary_image:
            return ProductImageSerializer(primary_image).data
        first_image = obj.images.first()
        if first_image:
            return ProductImageSerializer(first_image).data
        return None


class ProductDetailSerializer(serializers.ModelSerializer):
    store = StoreBasicSerializer(read_only=True)
    category = ProductCategorySerializer(read_only=True)
    images = ProductImageSerializer(many=True, read_only=True)
    variants = ProductVariantSerializer(many=True, read_only=True)
    reviews_count = serializers.SerializerMethodField()
    average_rating = serializers.SerializerMethodField()
    
    class Meta:
        model = Product
        fields = [
            'id', 'name', 'slug', 'description', 'short_description', 'price', 
            'compare_price', 'discount_percentage', 'sku', 'stock_quantity', 
            'weight', 'dimensions', 'is_in_stock', 'is_low_stock', 'rating',
            'total_sales', 'views_count', 'store', 'category', 'images', 
            'variants', 'reviews_count', 'average_rating', 'tags', 'meta_title', 
            'meta_description', 'is_featured', 'is_digital', 'requires_shipping'
        ]
    
    def get_reviews_count(self, obj):
        return obj.reviews.filter(is_approved=True).count()
    
    def get_average_rating(self, obj):
        reviews = obj.reviews.filter(is_approved=True)
        if reviews.exists():
            return reviews.aggregate(avg_rating=serializers.models.Avg('rating'))['avg_rating']
        return 0


class ProductReviewSerializer(serializers.ModelSerializer):
    user_name = serializers.CharField(source='user.username', read_only=True)
    
    class Meta:
        model = ProductReview
        fields = [
            'id', 'rating', 'title', 'comment', 'user_name', 'is_verified_purchase',
            'helpful_count', 'created_at'
        ]
        read_only_fields = ['user_name', 'is_verified_purchase', 'helpful_count', 'created_at']


class ProductCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Product
        fields = [
            'name', 'description', 'short_description', 'category', 'price', 
            'compare_price', 'cost_price', 'sku', 'barcode', 'stock_quantity',
            'low_stock_threshold', 'weight', 'dimensions', 'is_featured',
            'is_digital', 'requires_shipping', 'meta_title', 'meta_description', 'tags'
        ]
    
    def create(self, validated_data):
        # Set the store from the current user
        request = self.context.get('request')
        if request and request.user.is_vendor:
            store = request.user.stores.filter(status='approved').first()
            if store:
                validated_data['store'] = store
            else:
                raise serializers.ValidationError("لا يوجد متجر مُوافق عليه لهذا المستخدم")
        return super().create(validated_data)
