# Django Filter translation.
# Copyright (C) 2017
# This file is distributed under the same license as the django_filter package.
# <PERSON>, 2017.
#
msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-02-10 11:07+0000\n"
"PO-Revision-Date: 2023-06-30 13:51+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Portuguese (Brazil) <https://hosted.weblate.org/projects/"
"django-filter/django-filter/pt_BR/>\n"
"Language: pt_BR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n > 1;\n"
"X-Generator: Weblate 5.0-dev\n"

#: conf.py:16
msgid "date"
msgstr "data"

#: conf.py:17
msgid "year"
msgstr "ano"

#: conf.py:18
msgid "month"
msgstr "mês"

#: conf.py:19
msgid "day"
msgstr "dia"

#: conf.py:20
msgid "week day"
msgstr "dia da semana"

#: conf.py:21
msgid "hour"
msgstr "hora"

#: conf.py:22
msgid "minute"
msgstr "minuto"

#: conf.py:23
msgid "second"
msgstr "segundo"

#: conf.py:27 conf.py:28
msgid "contains"
msgstr "contém"

#: conf.py:29
msgid "is in"
msgstr "presente em"

#: conf.py:30
msgid "is greater than"
msgstr "é maior que"

#: conf.py:31
msgid "is greater than or equal to"
msgstr "é maior ou igual que"

#: conf.py:32
msgid "is less than"
msgstr "é menor que"

#: conf.py:33
msgid "is less than or equal to"
msgstr "é menor ou igual que"

#: conf.py:34 conf.py:35
msgid "starts with"
msgstr "começa com"

#: conf.py:36 conf.py:37
msgid "ends with"
msgstr "termina com"

#: conf.py:38
msgid "is in range"
msgstr "está no range"

#: conf.py:39
msgid "is null"
msgstr "é nulo"

#: conf.py:40 conf.py:41
msgid "matches regex"
msgstr "coincide com a expressão regular"

#: conf.py:42 conf.py:49
msgid "search"
msgstr "buscar"

#: conf.py:44
msgid "is contained by"
msgstr "está contido por"

#: conf.py:45
msgid "overlaps"
msgstr "sobrepõe"

#: conf.py:46
msgid "has key"
msgstr "contém a chave"

#: conf.py:47
msgid "has keys"
msgstr "contém as chaves"

#: conf.py:48
msgid "has any keys"
msgstr "contém uma das chaves"

#: fields.py:94
msgid "Select a lookup."
msgstr "Selecione uma pesquisa."

#: fields.py:198
msgid "Range query expects two values."
msgstr "Consulta por range requer dois valores."

#: filters.py:437
msgid "Today"
msgstr "Hoje"

#: filters.py:438
msgid "Yesterday"
msgstr "Ontem"

#: filters.py:439
msgid "Past 7 days"
msgstr "Últimos 7 dias"

#: filters.py:440
msgid "This month"
msgstr "Este mês"

#: filters.py:441
msgid "This year"
msgstr "Este ano"

#: filters.py:543
msgid "Multiple values may be separated by commas."
msgstr "Valores múltiplos podem ser separados por vírgulas."

#: filters.py:721
#, python-format
msgid "%s (descending)"
msgstr "%s (decrescente)"

#: filters.py:737
msgid "Ordering"
msgstr "Ordenado"

#: rest_framework/filterset.py:33
#: templates/django_filters/rest_framework/form.html:5
msgid "Submit"
msgstr "Enviar"

#: templates/django_filters/rest_framework/crispy_form.html:4
#: templates/django_filters/rest_framework/form.html:2
msgid "Field filters"
msgstr "Filtros de campo"

#: utils.py:308
msgid "exclude"
msgstr "excluir"

#: widgets.py:58
msgid "All"
msgstr "Tudo"

#: widgets.py:162
msgid "Unknown"
msgstr "Desconhecido"

#: widgets.py:162
msgid "Yes"
msgstr "Sim"

#: widgets.py:162
msgid "No"
msgstr "Não"

#~ msgid "Any date"
#~ msgstr "Qualquer data"
