#!/usr/bin/env python
"""
تشغيل مبسط للمشروع
Simple project runner
"""
import os
import sys
import subprocess

def main():
    print("🚀 تشغيل السوق الإلكتروني")
    print("=" * 30)
    
    # التحقق من manage.py
    if not os.path.exists('manage.py'):
        print("❌ ملف manage.py غير موجود")
        print("تأكد من أنك في مجلد المشروع الصحيح")
        return
    
    try:
        # تشغيل الخادم مباشرة
        print("🌐 تشغيل خادم Django...")
        print("الخادم سيعمل على: http://127.0.0.1:8000")
        print("لوحة التحكم: http://127.0.0.1:8000/admin/")
        print("اضغط Ctrl+C لإيقاف الخادم")
        print("-" * 30)
        
        subprocess.run([sys.executable, 'manage.py', 'runserver'])
        
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف الخادم")
    except FileNotFoundError:
        print("❌ Python غير موجود في PATH")
    except Exception as e:
        print(f"❌ خطأ: {e}")
        print("\n💡 جرب:")
        print("1. تفعيل البيئة الافتراضية:")
        print("   source marketplace_env/Scripts/activate")
        print("2. تثبيت المتطلبات:")
        print("   pip install django")
        print("3. تشغيل الإصلاحات:")
        print("   python fix_errors.py")

if __name__ == "__main__":
    main()
