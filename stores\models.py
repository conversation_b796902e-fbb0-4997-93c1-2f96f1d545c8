from django.db import models
from django.conf import settings
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils.text import slugify


class StoreCategory(models.Model):
    """
    تصنيفات المتاجر
    """
    name = models.CharField(
        max_length=100,
        unique=True,
        verbose_name=_('اسم التصنيف')
    )
    slug = models.SlugField(
        max_length=100,
        unique=True,
        blank=True,
        verbose_name=_('الرابط المختصر')
    )
    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('الوصف')
    )
    icon = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name=_('أيقونة')
    )
    is_active = models.BooleanField(
        default=True,
        verbose_name=_('نشط')
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    class Meta:
        verbose_name = _('تصنيف المتجر')
        verbose_name_plural = _('تصنيفات المتاجر')
        ordering = ['name']

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)


class Store(models.Model):
    """
    نموذج المتجر
    """
    STATUS_CHOICES = [
        ('pending', _('في انتظار الموافقة')),
        ('approved', _('مُوافق عليه')),
        ('suspended', _('مُعلق')),
        ('rejected', _('مرفوض')),
    ]

    owner = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='stores',
        verbose_name=_('صاحب المتجر')
    )
    name = models.CharField(
        max_length=200,
        verbose_name=_('اسم المتجر')
    )
    slug = models.SlugField(
        max_length=200,
        unique=True,
        blank=True,
        verbose_name=_('الرابط المختصر')
    )
    description = models.TextField(
        verbose_name=_('وصف المتجر')
    )
    category = models.ForeignKey(
        StoreCategory,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='stores',
        verbose_name=_('تصنيف المتجر')
    )
    logo = models.ImageField(
        upload_to='store_logos/',
        blank=True,
        null=True,
        verbose_name=_('شعار المتجر')
    )
    banner = models.ImageField(
        upload_to='store_banners/',
        blank=True,
        null=True,
        verbose_name=_('بانر المتجر')
    )
    address = models.TextField(
        verbose_name=_('عنوان المتجر')
    )
    phone = models.CharField(
        max_length=20,
        verbose_name=_('رقم الهاتف')
    )
    email = models.EmailField(
        verbose_name=_('البريد الإلكتروني')
    )
    website = models.URLField(
        blank=True,
        null=True,
        verbose_name=_('الموقع الإلكتروني')
    )
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        verbose_name=_('حالة المتجر')
    )
    commission_rate = models.DecimalField(
        max_digits=5,
        decimal_places=4,
        default=0.05,
        validators=[MinValueValidator(0), MaxValueValidator(1)],
        verbose_name=_('معدل العمولة')
    )
    is_featured = models.BooleanField(
        default=False,
        verbose_name=_('متجر مميز')
    )
    rating = models.DecimalField(
        max_digits=3,
        decimal_places=2,
        default=0.00,
        validators=[MinValueValidator(0), MaxValueValidator(5)],
        verbose_name=_('التقييم')
    )
    total_sales = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0.00,
        verbose_name=_('إجمالي المبيعات')
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )

    class Meta:
        verbose_name = _('متجر')
        verbose_name_plural = _('المتاجر')
        ordering = ['-created_at']

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)

    @property
    def is_active(self):
        return self.status == 'approved'

    @property
    def products_count(self):
        return self.products.filter(is_active=True).count()

    @property
    def orders_count(self):
        return self.orders.count()
