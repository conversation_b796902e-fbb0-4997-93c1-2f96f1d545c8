#!/usr/bin/env python
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'marketplace.settings')
django.setup()

from accounts.models import User

# Create admin user
if not User.objects.filter(username='admin').exists():
    User.objects.create_superuser(
        username='admin',
        email='<EMAIL>',
        password='admin123',
        user_type='admin'
    )
    print("Admin user created successfully!")
else:
    print("Admin user already exists!")
