#
msgid ""
msgstr ""
"Project-Id-Version: django-filter\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-02-10 11:07+0000\n"
"PO-Revision-Date: 2016-09-29 11:47+0300\n"
"Last-Translator: <PERSON><PERSON><PERSON> <eugen<PERSON>.<PERSON><PERSON><PERSON><PERSON>@gmail.com>\n"
"Language-Team: TextTempearture\n"
"Language: be\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n"
"%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n"
"%100>=11 && n%100<=14)? 2 : 3);\n"
"X-Generator: Poedit 1.8.9\n"

#: conf.py:16
msgid "date"
msgstr "дата"

#: conf.py:17
msgid "year"
msgstr "год"

#: conf.py:18
msgid "month"
msgstr "месяц"

#: conf.py:19
msgid "day"
msgstr "дзень"

#: conf.py:20
msgid "week day"
msgstr "дзень тыдня"

#: conf.py:21
msgid "hour"
msgstr "гадзіну"

#: conf.py:22
msgid "minute"
msgstr "хвіліна"

#: conf.py:23
msgid "second"
msgstr "секунда"

#: conf.py:27 conf.py:28
msgid "contains"
msgstr "змяшчае"

#: conf.py:29
msgid "is in"
msgstr "у"

#: conf.py:30
msgid "is greater than"
msgstr "больш чым"

#: conf.py:31
msgid "is greater than or equal to"
msgstr "больш або роўна"

#: conf.py:32
msgid "is less than"
msgstr "менш чым"

#: conf.py:33
msgid "is less than or equal to"
msgstr "менш або роўна"

#: conf.py:34 conf.py:35
msgid "starts with"
msgstr "пачынаецца"

#: conf.py:36 conf.py:37
msgid "ends with"
msgstr "заканчваецца"

#: conf.py:38
msgid "is in range"
msgstr "у дыяпазоне"

#: conf.py:39
msgid "is null"
msgstr ""

#: conf.py:40 conf.py:41
msgid "matches regex"
msgstr "адпавядае рэгулярнаму выразу"

#: conf.py:42 conf.py:49
msgid "search"
msgstr "пошук"

#: conf.py:44
msgid "is contained by"
msgstr "змяшчаецца ў"

#: conf.py:45
msgid "overlaps"
msgstr "перакрываецца"

#: conf.py:46
msgid "has key"
msgstr "мае ключ"

#: conf.py:47
msgid "has keys"
msgstr "мае ключы"

#: conf.py:48
msgid "has any keys"
msgstr "мае любыя ключы"

#: fields.py:94
msgid "Select a lookup."
msgstr ""

#: fields.py:198
msgid "Range query expects two values."
msgstr "Запыт дыяпазону чакае два значэння."

#: filters.py:437
msgid "Today"
msgstr "Сёння"

#: filters.py:438
msgid "Yesterday"
msgstr "Учора"

#: filters.py:439
msgid "Past 7 days"
msgstr "Мінулыя 7 дзён"

#: filters.py:440
msgid "This month"
msgstr "За гэты месяц"

#: filters.py:441
msgid "This year"
msgstr "У гэтым годзе"

#: filters.py:543
msgid "Multiple values may be separated by commas."
msgstr "Некалькі значэнняў могуць быць падзеленыя коскамі."

#: filters.py:721
#, python-format
msgid "%s (descending)"
msgstr "%s (па змяншэнні)"

#: filters.py:737
msgid "Ordering"
msgstr "Парадак"

#: rest_framework/filterset.py:33
#: templates/django_filters/rest_framework/form.html:5
msgid "Submit"
msgstr "Адправіць"

#: templates/django_filters/rest_framework/crispy_form.html:4
#: templates/django_filters/rest_framework/form.html:2
msgid "Field filters"
msgstr "Фільтры па палях"

#: utils.py:308
msgid "exclude"
msgstr "выключаючы"

#: widgets.py:58
msgid "All"
msgstr "Усе"

#: widgets.py:162
msgid "Unknown"
msgstr "Не было прапанавана"

#: widgets.py:162
msgid "Yes"
msgstr "Ды"

#: widgets.py:162
msgid "No"
msgstr "Няма"

#~ msgid "Any date"
#~ msgstr "Любая дата"
