from django.db import models
from django.conf import settings
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator
from products.models import Product, ProductVariant
from stores.models import Store
import uuid


class Cart(models.Model):
    """
    سلة المشتريات
    """
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='carts',
        verbose_name=_('المستخدم')
    )
    session_key = models.CharField(
        max_length=40,
        blank=True,
        null=True,
        verbose_name=_('مفتاح الجلسة')
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )

    class Meta:
        verbose_name = _('سلة المشتريات')
        verbose_name_plural = _('سلال المشتريات')

    def __str__(self):
        return f"سلة {self.user.username if self.user else self.session_key}"

    @property
    def total_items(self):
        return sum(item.quantity for item in self.items.all())

    @property
    def subtotal(self):
        return sum(item.total_price for item in self.items.all())

    @property
    def total_commission(self):
        return sum(item.commission_amount for item in self.items.all())

    @property
    def total_price(self):
        return self.subtotal


class CartItem(models.Model):
    """
    عنصر في سلة المشتريات
    """
    cart = models.ForeignKey(
        Cart,
        on_delete=models.CASCADE,
        related_name='items',
        verbose_name=_('السلة')
    )
    product = models.ForeignKey(
        Product,
        on_delete=models.CASCADE,
        verbose_name=_('المنتج')
    )
    variant = models.ForeignKey(
        ProductVariant,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name=_('متغير المنتج')
    )
    quantity = models.PositiveIntegerField(
        default=1,
        validators=[MinValueValidator(1)],
        verbose_name=_('الكمية')
    )
    price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_('السعر')
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإضافة')
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )

    class Meta:
        verbose_name = _('عنصر السلة')
        verbose_name_plural = _('عناصر السلة')
        unique_together = ['cart', 'product', 'variant']

    def __str__(self):
        return f"{self.product.name} x {self.quantity}"

    @property
    def total_price(self):
        return self.price * self.quantity

    @property
    def commission_rate(self):
        return self.product.store.commission_rate

    @property
    def commission_amount(self):
        return self.total_price * self.commission_rate

    def save(self, *args, **kwargs):
        if not self.price:
            if self.variant:
                self.price = self.variant.final_price
            else:
                self.price = self.product.price
        super().save(*args, **kwargs)


class Order(models.Model):
    """
    الطلب
    """
    STATUS_CHOICES = [
        ('pending', _('في انتظار الدفع')),
        ('paid', _('مدفوع')),
        ('processing', _('قيد المعالجة')),
        ('shipped', _('تم الشحن')),
        ('delivered', _('تم التسليم')),
        ('cancelled', _('ملغي')),
        ('refunded', _('مُسترد')),
    ]

    id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False
    )
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='orders',
        verbose_name=_('المستخدم')
    )
    order_number = models.CharField(
        max_length=20,
        unique=True,
        verbose_name=_('رقم الطلب')
    )
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        verbose_name=_('حالة الطلب')
    )

    # Billing Information
    billing_first_name = models.CharField(
        max_length=50,
        verbose_name=_('الاسم الأول للفاتورة')
    )
    billing_last_name = models.CharField(
        max_length=50,
        verbose_name=_('اسم العائلة للفاتورة')
    )
    billing_email = models.EmailField(
        verbose_name=_('البريد الإلكتروني للفاتورة')
    )
    billing_phone = models.CharField(
        max_length=20,
        verbose_name=_('رقم الهاتف للفاتورة')
    )
    billing_address = models.TextField(
        verbose_name=_('عنوان الفاتورة')
    )
    billing_city = models.CharField(
        max_length=100,
        verbose_name=_('مدينة الفاتورة')
    )
    billing_postal_code = models.CharField(
        max_length=20,
        verbose_name=_('الرمز البريدي للفاتورة')
    )
    billing_country = models.CharField(
        max_length=100,
        verbose_name=_('بلد الفاتورة')
    )

    # Shipping Information
    shipping_first_name = models.CharField(
        max_length=50,
        verbose_name=_('الاسم الأول للشحن')
    )
    shipping_last_name = models.CharField(
        max_length=50,
        verbose_name=_('اسم العائلة للشحن')
    )
    shipping_phone = models.CharField(
        max_length=20,
        verbose_name=_('رقم الهاتف للشحن')
    )
    shipping_address = models.TextField(
        verbose_name=_('عنوان الشحن')
    )
    shipping_city = models.CharField(
        max_length=100,
        verbose_name=_('مدينة الشحن')
    )
    shipping_postal_code = models.CharField(
        max_length=20,
        verbose_name=_('الرمز البريدي للشحن')
    )
    shipping_country = models.CharField(
        max_length=100,
        verbose_name=_('بلد الشحن')
    )

    # Order Totals
    subtotal = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        verbose_name=_('المجموع الفرعي')
    )
    shipping_cost = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0.00,
        verbose_name=_('تكلفة الشحن')
    )
    tax_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0.00,
        verbose_name=_('مبلغ الضريبة')
    )
    total_commission = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        verbose_name=_('إجمالي العمولة')
    )
    total_amount = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        verbose_name=_('المبلغ الإجمالي')
    )

    # Additional Information
    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('ملاحظات')
    )
    tracking_number = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('رقم التتبع')
    )

    # Timestamps
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )
    shipped_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('تاريخ الشحن')
    )
    delivered_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('تاريخ التسليم')
    )

    class Meta:
        verbose_name = _('طلب')
        verbose_name_plural = _('الطلبات')
        ordering = ['-created_at']

    def __str__(self):
        return f"طلب #{self.order_number}"

    def save(self, *args, **kwargs):
        if not self.order_number:
            # Generate unique order number
            import random
            import string
            self.order_number = ''.join(random.choices(string.digits, k=10))
        super().save(*args, **kwargs)

    @property
    def stores(self):
        """Get all stores involved in this order"""
        return Store.objects.filter(
            products__orderitem__order=self
        ).distinct()

    @property
    def can_be_cancelled(self):
        return self.status in ['pending', 'paid']


class OrderItem(models.Model):
    """
    عنصر في الطلب
    """
    order = models.ForeignKey(
        Order,
        on_delete=models.CASCADE,
        related_name='items',
        verbose_name=_('الطلب')
    )
    product = models.ForeignKey(
        Product,
        on_delete=models.CASCADE,
        verbose_name=_('المنتج')
    )
    variant = models.ForeignKey(
        ProductVariant,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name=_('متغير المنتج')
    )
    quantity = models.PositiveIntegerField(
        validators=[MinValueValidator(1)],
        verbose_name=_('الكمية')
    )
    price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_('السعر')
    )
    commission_rate = models.DecimalField(
        max_digits=5,
        decimal_places=4,
        verbose_name=_('معدل العمولة')
    )
    commission_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_('مبلغ العمولة')
    )

    # Store snapshot data at time of order
    store_name = models.CharField(
        max_length=200,
        verbose_name=_('اسم المتجر')
    )
    product_name = models.CharField(
        max_length=200,
        verbose_name=_('اسم المنتج')
    )
    product_sku = models.CharField(
        max_length=100,
        verbose_name=_('رمز المنتج')
    )

    class Meta:
        verbose_name = _('عنصر الطلب')
        verbose_name_plural = _('عناصر الطلب')

    def __str__(self):
        return f"{self.product_name} x {self.quantity}"

    @property
    def total_price(self):
        return self.price * self.quantity

    @property
    def store_amount(self):
        """Amount that goes to the store (after commission)"""
        return self.total_price - self.commission_amount

    def save(self, *args, **kwargs):
        if not self.commission_rate:
            self.commission_rate = self.product.store.commission_rate
        if not self.commission_amount:
            self.commission_amount = self.total_price * self.commission_rate
        if not self.store_name:
            self.store_name = self.product.store.name
        if not self.product_name:
            self.product_name = self.product.name
        if not self.product_sku:
            self.product_sku = self.product.sku
        super().save(*args, **kwargs)


class OrderStatusHistory(models.Model):
    """
    تاريخ حالات الطلب
    """
    order = models.ForeignKey(
        Order,
        on_delete=models.CASCADE,
        related_name='status_history',
        verbose_name=_('الطلب')
    )
    status = models.CharField(
        max_length=20,
        choices=Order.STATUS_CHOICES,
        verbose_name=_('الحالة')
    )
    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('ملاحظات')
    )
    changed_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('تم التغيير بواسطة')
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ التغيير')
    )

    class Meta:
        verbose_name = _('تاريخ حالة الطلب')
        verbose_name_plural = _('تاريخ حالات الطلبات')
        ordering = ['-created_at']

    def __str__(self):
        return f"طلب #{self.order.order_number} - {self.get_status_display()}"
