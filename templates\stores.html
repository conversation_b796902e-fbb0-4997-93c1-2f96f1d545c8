<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المتاجر - السوق الإلكتروني</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .store-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: none;
            border-radius: 15px;
            overflow: hidden;
            height: 100%;
        }
        .store-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }
        .store-logo {
            width: 80px;
            height: 80px;
            object-fit: cover;
            border-radius: 50%;
            border: 3px solid #fff;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        .store-banner {
            height: 150px;
            object-fit: cover;
            width: 100%;
        }
        .rating {
            color: #ffc107;
        }
        .featured-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        .store-stats {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-top: 15px;
        }
        .filter-sidebar {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-store"></i> السوق الإلكتروني
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/products/">المنتجات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/stores/">المتاجر</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="/cart/">
                            <i class="fas fa-shopping-cart"></i> السلة <span class="badge bg-primary" id="cartCount">0</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/login/">
                            <i class="fas fa-user"></i> تسجيل الدخول
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-lg-3 mb-4">
                <div class="filter-sidebar">
                    <h5 class="mb-3">
                        <i class="fas fa-filter"></i> البحث والفلترة
                    </h5>
                    
                    <!-- Search -->
                    <div class="mb-3">
                        <input type="text" class="form-control" id="searchInput" placeholder="ابحث عن المتاجر...">
                    </div>
                    
                    <!-- Categories -->
                    <div class="mb-3">
                        <h6>تصنيفات المتاجر</h6>
                        <div id="categoriesFilter">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" value="electronics" id="electronics">
                                <label class="form-check-label" for="electronics">إلكترونيات</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" value="fashion" id="fashion">
                                <label class="form-check-label" for="fashion">أزياء وموضة</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" value="books" id="books">
                                <label class="form-check-label" for="books">كتب ومكتبات</label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Rating -->
                    <div class="mb-3">
                        <h6>التقييم</h6>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" value="5" id="rating5">
                            <label class="form-check-label" for="rating5">
                                <span class="rating">★★★★★</span>
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" value="4" id="rating4">
                            <label class="form-check-label" for="rating4">
                                <span class="rating">★★★★</span> فأكثر
                            </label>
                        </div>
                    </div>
                    
                    <!-- Featured -->
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="featuredOnly">
                            <label class="form-check-label" for="featuredOnly">
                                المتاجر المميزة فقط
                            </label>
                        </div>
                    </div>
                    
                    <button class="btn btn-primary w-100" onclick="applyFilters()">
                        <i class="fas fa-search"></i> تطبيق الفلاتر
                    </button>
                </div>
            </div>
            
            <!-- Stores -->
            <div class="col-lg-9">
                <!-- Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>المتاجر</h2>
                    <div class="d-flex align-items-center">
                        <label for="sortBy" class="me-2">ترتيب حسب:</label>
                        <select class="form-select" id="sortBy" style="width: auto;">
                            <option value="newest">الأحدث</option>
                            <option value="rating">التقييم</option>
                            <option value="name">الاسم</option>
                            <option value="products_count">عدد المنتجات</option>
                        </select>
                    </div>
                </div>
                
                <!-- Stores Grid -->
                <div class="row" id="storesGrid">
                    <!-- Stores will be loaded here via JavaScript -->
                    <div class="col-12 text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                        <p class="mt-3">جاري تحميل المتاجر...</p>
                    </div>
                </div>
                
                <!-- Pagination -->
                <nav aria-label="صفحات المتاجر" class="mt-4">
                    <ul class="pagination justify-content-center" id="pagination">
                        <!-- Pagination will be generated by JavaScript -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container text-center">
            <p class="mb-0">&copy; 2024 السوق الإلكتروني. جميع الحقوق محفوظة.</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Sample stores data (replace with API call)
        const sampleStores = [
            {
                id: 1,
                name: 'متجر التقنية الحديثة',
                description: 'متجر متخصص في بيع أحدث الأجهزة الإلكترونية والتقنية',
                category: 'إلكترونيات',
                logo: '/static/images/store-logo-1.jpg',
                banner: '/static/images/store-banner-1.jpg',
                rating: 4.5,
                products_count: 25,
                is_featured: true,
                owner: 'أحمد محمد'
            },
            {
                id: 2,
                name: 'متجر الأزياء العصرية',
                description: 'أحدث صيحات الموضة والأزياء للرجال والنساء',
                category: 'أزياء وموضة',
                logo: '/static/images/store-logo-2.jpg',
                banner: '/static/images/store-banner-2.jpg',
                rating: 4.2,
                products_count: 18,
                is_featured: false,
                owner: 'سارة أحمد'
            }
        ];
        
        function loadStores(page = 1, filters = {}) {
            // Simulate API call
            setTimeout(() => {
                displayStores(sampleStores);
            }, 500);
        }
        
        function displayStores(stores) {
            const grid = document.getElementById('storesGrid');
            
            if (stores.length === 0) {
                grid.innerHTML = `
                    <div class="col-12 text-center py-5">
                        <i class="fas fa-store fa-3x text-muted mb-3"></i>
                        <h5>لا توجد متاجر</h5>
                        <p>لم يتم العثور على متاجر تطابق معايير البحث</p>
                    </div>
                `;
                return;
            }
            
            grid.innerHTML = stores.map(store => `
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card store-card h-100 position-relative">
                        ${store.is_featured ? '<div class="featured-badge">متجر مميز</div>' : ''}
                        <img src="${store.banner || '/static/images/default-banner.jpg'}" 
                             class="store-banner" alt="${store.name}">
                        <div class="card-body text-center">
                            <div class="position-relative d-inline-block mb-3">
                                <img src="${store.logo || '/static/images/default-logo.jpg'}" 
                                     class="store-logo" alt="${store.name}">
                            </div>
                            <h5 class="card-title">${store.name}</h5>
                            <p class="card-text text-muted small">${store.description}</p>
                            <div class="mb-2">
                                <span class="badge bg-secondary">${store.category}</span>
                            </div>
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <div class="rating">
                                    ${'★'.repeat(Math.floor(store.rating))}${'☆'.repeat(5 - Math.floor(store.rating))}
                                    <small class="text-muted">(${store.rating})</small>
                                </div>
                                <small class="text-muted">${store.products_count} منتج</small>
                            </div>
                            <div class="store-stats">
                                <div class="row text-center">
                                    <div class="col-6">
                                        <strong>${store.products_count}</strong>
                                        <br><small>منتج</small>
                                    </div>
                                    <div class="col-6">
                                        <strong>${store.rating}</strong>
                                        <br><small>تقييم</small>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-3">
                                <a href="/stores/${store.id}/" class="btn btn-primary w-100">
                                    <i class="fas fa-eye"></i> زيارة المتجر
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');
        }
        
        function applyFilters() {
            const filters = {
                search: document.getElementById('searchInput').value,
                featured: document.getElementById('featuredOnly').checked,
                ordering: document.getElementById('sortBy').value
            };
            
            loadStores(1, filters);
        }
        
        // Load stores on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadStores();
        });
        
        // Apply filters on sort change
        document.getElementById('sortBy').addEventListener('change', applyFilters);
    </script>
</body>
</html>
