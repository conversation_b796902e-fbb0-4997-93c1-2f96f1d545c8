#!/usr/bin/env python
"""
إنشاء بيانات تجريبية للمشروع
Create sample data for the project
"""
import os
import django
from decimal import Decimal

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'marketplace.settings')
django.setup()

from accounts.models import User
from stores.models import StoreCategory, Store
from products.models import ProductCategory, Product

def create_sample_data():
    print("🔄 إنشاء بيانات تجريبية...")
    
    # إنشاء مستخدمين تجريبيين
    print("👥 إنشاء مستخدمين...")
    
    # عميل تجريبي
    customer, created = User.objects.get_or_create(
        username='customer1',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'أحمد',
            'last_name': 'محمد',
            'user_type': 'customer',
            'phone': '**********',
            'is_verified': True
        }
    )
    if created:
        customer.set_password('customer123')
        customer.save()
        print("✅ تم إنشاء العميل التجريبي: customer1 / customer123")
    
    # صاحب متجر تجريبي
    vendor, created = User.objects.get_or_create(
        username='vendor1',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'سارة',
            'last_name': 'أحمد',
            'user_type': 'vendor',
            'phone': '0507654321',
            'is_verified': True
        }
    )
    if created:
        vendor.set_password('vendor123')
        vendor.save()
        print("✅ تم إنشاء صاحب المتجر التجريبي: vendor1 / vendor123")
    
    # إنشاء تصنيفات المتاجر
    print("🏪 إنشاء تصنيفات المتاجر...")
    
    electronics_cat, _ = StoreCategory.objects.get_or_create(
        name='إلكترونيات',
        defaults={
            'description': 'متاجر الأجهزة الإلكترونية والتقنية',
            'icon': 'fas fa-laptop'
        }
    )
    
    fashion_cat, _ = StoreCategory.objects.get_or_create(
        name='أزياء وموضة',
        defaults={
            'description': 'متاجر الملابس والأزياء',
            'icon': 'fas fa-tshirt'
        }
    )
    
    books_cat, _ = StoreCategory.objects.get_or_create(
        name='كتب ومكتبات',
        defaults={
            'description': 'متاجر الكتب والمواد التعليمية',
            'icon': 'fas fa-book'
        }
    )
    
    # إنشاء متجر تجريبي
    print("🏬 إنشاء متجر تجريبي...")
    
    store, created = Store.objects.get_or_create(
        name='متجر التقنية الحديثة',
        owner=vendor,
        defaults={
            'description': 'متجر متخصص في بيع أحدث الأجهزة الإلكترونية والتقنية',
            'category': electronics_cat,
            'address': 'الرياض، حي العليا، شارع الملك فهد',
            'phone': '0112345678',
            'email': '<EMAIL>',
            'status': 'approved',
            'commission_rate': Decimal('0.05'),
            'is_featured': True,
            'rating': Decimal('4.5')
        }
    )
    if created:
        print("✅ تم إنشاء المتجر التجريبي: متجر التقنية الحديثة")
    
    # إنشاء تصنيفات المنتجات
    print("📦 إنشاء تصنيفات المنتجات...")
    
    smartphones_cat, _ = ProductCategory.objects.get_or_create(
        name='هواتف ذكية',
        defaults={
            'description': 'أحدث الهواتف الذكية والجوالات',
            'sort_order': 1
        }
    )
    
    laptops_cat, _ = ProductCategory.objects.get_or_create(
        name='أجهزة كمبيوتر محمولة',
        defaults={
            'description': 'أجهزة الكمبيوتر المحمولة واللابتوب',
            'sort_order': 2
        }
    )
    
    accessories_cat, _ = ProductCategory.objects.get_or_create(
        name='إكسسوارات',
        defaults={
            'description': 'إكسسوارات الأجهزة الإلكترونية',
            'sort_order': 3
        }
    )
    
    # إنشاء منتجات تجريبية
    print("📱 إنشاء منتجات تجريبية...")
    
    products_data = [
        {
            'name': 'iPhone 15 Pro',
            'description': 'أحدث هاتف من آبل بتقنية متطورة وكاميرا احترافية',
            'short_description': 'هاتف آيفون 15 برو بذاكرة 256 جيجا',
            'category': smartphones_cat,
            'price': Decimal('4999.00'),
            'compare_price': Decimal('5499.00'),
            'sku': 'IP15P-256-BLU',
            'stock_quantity': 25,
            'is_featured': True,
            'rating': Decimal('4.8')
        },
        {
            'name': 'Samsung Galaxy S24 Ultra',
            'description': 'هاتف سامسونج الرائد بقلم S Pen وكاميرا 200 ميجابكسل',
            'short_description': 'هاتف سامسونج جالاكسي S24 الترا',
            'category': smartphones_cat,
            'price': Decimal('4299.00'),
            'compare_price': Decimal('4799.00'),
            'sku': 'SGS24U-512-BLK',
            'stock_quantity': 18,
            'is_featured': True,
            'rating': Decimal('4.7')
        },
        {
            'name': 'MacBook Pro 16 inch',
            'description': 'جهاز MacBook Pro بمعالج M3 Pro وشاشة Retina عالية الدقة',
            'short_description': 'ماك بوك برو 16 بوصة بمعالج M3',
            'category': laptops_cat,
            'price': Decimal('8999.00'),
            'compare_price': Decimal('9999.00'),
            'sku': 'MBP16-M3-1TB',
            'stock_quantity': 12,
            'is_featured': True,
            'rating': Decimal('4.9')
        },
        {
            'name': 'AirPods Pro 3',
            'description': 'سماعات آبل اللاسلكية مع إلغاء الضوضاء النشط',
            'short_description': 'سماعات آيربودز برو الجيل الثالث',
            'category': accessories_cat,
            'price': Decimal('899.00'),
            'compare_price': Decimal('999.00'),
            'sku': 'APP3-WHT',
            'stock_quantity': 50,
            'is_featured': False,
            'rating': Decimal('4.6')
        },
        {
            'name': 'Dell XPS 13',
            'description': 'جهاز كمبيوتر محمول أنيق وقوي للمحترفين',
            'short_description': 'ديل XPS 13 بمعالج Intel i7',
            'category': laptops_cat,
            'price': Decimal('3999.00'),
            'sku': 'DXPS13-I7-512',
            'stock_quantity': 8,
            'is_featured': False,
            'rating': Decimal('4.4')
        }
    ]
    
    for product_data in products_data:
        product, created = Product.objects.get_or_create(
            name=product_data['name'],
            store=store,
            defaults=product_data
        )
        if created:
            print(f"✅ تم إنشاء المنتج: {product.name}")
    
    print("\n🎉 تم إنشاء البيانات التجريبية بنجاح!")
    print("=" * 50)
    print("📋 البيانات المُنشأة:")
    print("👤 العملاء:")
    print("   • customer1 / customer123")
    print("🏪 أصحاب المتاجر:")
    print("   • vendor1 / vendor123")
    print("🏬 المتاجر:")
    print("   • متجر التقنية الحديثة")
    print("📱 المنتجات:")
    print(f"   • {Product.objects.count()} منتج تجريبي")
    print("\n🌐 يمكنك الآن تصفح الموقع ومشاهدة البيانات!")

if __name__ == "__main__":
    create_sample_data()
