<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة منتج جديد - السوق الإلكتروني</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .form-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        .form-floating label {
            right: 12px;
            left: auto;
        }
        .image-upload-area {
            border: 2px dashed #ddd;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 15px;
        }
        .image-upload-area:hover {
            border-color: #007bff;
            background-color: #f8f9ff;
        }
        .image-upload-area.dragover {
            border-color: #007bff;
            background-color: #e3f2fd;
        }
        .image-preview {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 15px;
        }
        .image-preview-item {
            position: relative;
            width: 120px;
            height: 120px;
            border-radius: 10px;
            overflow: hidden;
            border: 2px solid #ddd;
        }
        .image-preview-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .image-preview-item .remove-btn {
            position: absolute;
            top: 5px;
            right: 5px;
            background: #dc3545;
            color: white;
            border: none;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            font-size: 12px;
            cursor: pointer;
        }
        .image-preview-item .primary-btn {
            position: absolute;
            bottom: 5px;
            left: 5px;
            background: #28a745;
            color: white;
            border: none;
            border-radius: 15px;
            padding: 2px 8px;
            font-size: 10px;
            cursor: pointer;
        }
        .variant-row {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            border: 1px solid #e9ecef;
        }
        .tag-input {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            padding: 8px;
            border: 1px solid #ced4da;
            border-radius: 5px;
            min-height: 45px;
            align-items: center;
        }
        .tag {
            background: #007bff;
            color: white;
            padding: 4px 8px;
            border-radius: 15px;
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .tag .remove-tag {
            cursor: pointer;
            font-weight: bold;
        }
        .tag-input input {
            border: none;
            outline: none;
            flex: 1;
            min-width: 100px;
        }
        .progress-steps {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
        }
        .step {
            display: flex;
            align-items: center;
            margin: 0 15px;
        }
        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            color: #6c757d;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-left: 10px;
        }
        .step.active .step-number {
            background: #007bff;
            color: white;
        }
        .step.completed .step-number {
            background: #28a745;
            color: white;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-store"></i> السوق الإلكتروني
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/vendor/dashboard/">
                    <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                </a>
                <a class="nav-link" href="/vendor/products/">
                    <i class="fas fa-box"></i> منتجاتي
                </a>
                <a class="nav-link" href="/logout/">
                    <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h2 class="mb-4">
                    <i class="fas fa-plus-circle"></i> إضافة منتج جديد
                </h2>
            </div>
        </div>

        <!-- Progress Steps -->
        <div class="progress-steps">
            <div class="step active">
                <div class="step-number">1</div>
                <span>المعلومات الأساسية</span>
            </div>
            <div class="step">
                <div class="step-number">2</div>
                <span>الصور والوسائط</span>
            </div>
            <div class="step">
                <div class="step-number">3</div>
                <span>التسعير والمخزون</span>
            </div>
            <div class="step">
                <div class="step-number">4</div>
                <span>المراجعة والنشر</span>
            </div>
        </div>

        <form id="productForm">
            <!-- Basic Information -->
            <div class="form-section" id="step1">
                <h4 class="mb-4">
                    <i class="fas fa-info-circle"></i> المعلومات الأساسية
                </h4>
                
                <div class="row">
                    <div class="col-md-8">
                        <div class="form-floating mb-3">
                            <input type="text" class="form-control" id="productName" required>
                            <label for="productName">اسم المنتج *</label>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-floating mb-3">
                            <select class="form-select" id="category" required>
                                <option value="">اختر التصنيف</option>
                                <option value="electronics">إلكترونيات</option>
                                <option value="clothing">ملابس</option>
                                <option value="books">كتب</option>
                                <option value="home">منزل وحديقة</option>
                                <option value="sports">رياضة</option>
                            </select>
                            <label for="category">التصنيف *</label>
                        </div>
                    </div>
                </div>
                
                <div class="form-floating mb-3">
                    <textarea class="form-control" id="shortDescription" style="height: 80px"></textarea>
                    <label for="shortDescription">وصف مختصر</label>
                </div>
                
                <div class="form-floating mb-3">
                    <textarea class="form-control" id="description" style="height: 150px" required></textarea>
                    <label for="description">الوصف التفصيلي *</label>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-floating mb-3">
                            <input type="text" class="form-control" id="sku" required>
                            <label for="sku">رمز المنتج (SKU) *</label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-floating mb-3">
                            <input type="text" class="form-control" id="barcode">
                            <label for="barcode">الباركود</label>
                        </div>
                    </div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">العلامات (Tags)</label>
                    <div class="tag-input" id="tagInput">
                        <input type="text" placeholder="اكتب علامة واضغط Enter" onkeypress="addTag(event)">
                    </div>
                    <small class="text-muted">اضغط Enter لإضافة علامة جديدة</small>
                </div>
            </div>

            <!-- Images and Media -->
            <div class="form-section" id="step2" style="display: none;">
                <h4 class="mb-4">
                    <i class="fas fa-images"></i> الصور والوسائط
                </h4>
                
                <div class="image-upload-area" onclick="document.getElementById('imageInput').click()">
                    <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                    <h5>اسحب الصور هنا أو انقر للاختيار</h5>
                    <p class="text-muted">يمكنك رفع عدة صور (PNG, JPG, JPEG)</p>
                    <input type="file" id="imageInput" multiple accept="image/*" style="display: none;" onchange="handleImageUpload(event)">
                </div>
                
                <div class="image-preview" id="imagePreview">
                    <!-- Image previews will be added here -->
                </div>
                
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>نصائح للصور:</strong>
                    <ul class="mb-0 mt-2">
                        <li>استخدم صور عالية الجودة (1200x1200 بكسل على الأقل)</li>
                        <li>تأكد من وضوح المنتج في الصورة</li>
                        <li>أضف صور من زوايا مختلفة</li>
                        <li>الصورة الأولى ستكون الصورة الرئيسية</li>
                    </ul>
                </div>
            </div>

            <!-- Pricing and Inventory -->
            <div class="form-section" id="step3" style="display: none;">
                <h4 class="mb-4">
                    <i class="fas fa-dollar-sign"></i> التسعير والمخزون
                </h4>
                
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-floating mb-3">
                            <input type="number" class="form-control" id="price" step="0.01" min="0" required>
                            <label for="price">السعر (ر.س) *</label>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-floating mb-3">
                            <input type="number" class="form-control" id="comparePrice" step="0.01" min="0">
                            <label for="comparePrice">سعر المقارنة (ر.س)</label>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-floating mb-3">
                            <input type="number" class="form-control" id="costPrice" step="0.01" min="0">
                            <label for="costPrice">سعر التكلفة (ر.س)</label>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-floating mb-3">
                            <input type="number" class="form-control" id="stockQuantity" min="0" required>
                            <label for="stockQuantity">كمية المخزون *</label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-floating mb-3">
                            <input type="number" class="form-control" id="lowStockThreshold" min="0" value="5">
                            <label for="lowStockThreshold">حد التنبيه للمخزون المنخفض</label>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-floating mb-3">
                            <input type="number" class="form-control" id="weight" step="0.01" min="0">
                            <label for="weight">الوزن (كجم)</label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check mt-3">
                            <input class="form-check-input" type="checkbox" id="requiresShipping" checked>
                            <label class="form-check-label" for="requiresShipping">
                                يتطلب شحن
                            </label>
                        </div>
                    </div>
                </div>
                
                <!-- Product Variants -->
                <div class="mt-4">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5>متغيرات المنتج</h5>
                        <button type="button" class="btn btn-outline-primary" onclick="addVariant()">
                            <i class="fas fa-plus"></i> إضافة متغير
                        </button>
                    </div>
                    
                    <div id="variantsContainer">
                        <!-- Variants will be added here -->
                    </div>
                </div>
            </div>

            <!-- Review and Publish -->
            <div class="form-section" id="step4" style="display: none;">
                <h4 class="mb-4">
                    <i class="fas fa-eye"></i> المراجعة والنشر
                </h4>
                
                <div class="row">
                    <div class="col-md-8">
                        <div id="productPreview">
                            <!-- Product preview will be generated here -->
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-title">إعدادات النشر</h6>
                                
                                <div class="form-floating mb-3">
                                    <select class="form-select" id="status">
                                        <option value="draft">مسودة</option>
                                        <option value="active">نشط</option>
                                        <option value="inactive">غير نشط</option>
                                    </select>
                                    <label for="status">حالة المنتج</label>
                                </div>
                                
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="isFeatured">
                                    <label class="form-check-label" for="isFeatured">
                                        منتج مميز
                                    </label>
                                </div>
                                
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="isDigital">
                                    <label class="form-check-label" for="isDigital">
                                        منتج رقمي
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Navigation Buttons -->
            <div class="d-flex justify-content-between mt-4">
                <button type="button" class="btn btn-outline-secondary" id="prevBtn" onclick="previousStep()" style="display: none;">
                    <i class="fas fa-arrow-right"></i> السابق
                </button>
                
                <div class="ms-auto">
                    <button type="button" class="btn btn-primary" id="nextBtn" onclick="nextStep()">
                        التالي <i class="fas fa-arrow-left"></i>
                    </button>
                    
                    <button type="submit" class="btn btn-success" id="submitBtn" style="display: none;">
                        <i class="fas fa-save"></i> حفظ المنتج
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container text-center">
            <p class="mb-0">&copy; 2024 السوق الإلكتروني. جميع الحقوق محفوظة.</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentStep = 1;
        let uploadedImages = [];
        let tags = [];
        let variants = [];

        function nextStep() {
            if (validateCurrentStep()) {
                if (currentStep < 4) {
                    currentStep++;
                    updateStepDisplay();
                }
            }
        }

        function previousStep() {
            if (currentStep > 1) {
                currentStep--;
                updateStepDisplay();
            }
        }

        function updateStepDisplay() {
            // Hide all steps
            for (let i = 1; i <= 4; i++) {
                document.getElementById(`step${i}`).style.display = 'none';
            }
            
            // Show current step
            document.getElementById(`step${currentStep}`).style.display = 'block';
            
            // Update step indicators
            document.querySelectorAll('.step').forEach((step, index) => {
                step.classList.remove('active', 'completed');
                if (index + 1 < currentStep) {
                    step.classList.add('completed');
                } else if (index + 1 === currentStep) {
                    step.classList.add('active');
                }
            });
            
            // Update navigation buttons
            document.getElementById('prevBtn').style.display = currentStep > 1 ? 'block' : 'none';
            document.getElementById('nextBtn').style.display = currentStep < 4 ? 'block' : 'none';
            document.getElementById('submitBtn').style.display = currentStep === 4 ? 'block' : 'none';
            
            // Generate preview on last step
            if (currentStep === 4) {
                generateProductPreview();
            }
        }

        function validateCurrentStep() {
            const step = document.getElementById(`step${currentStep}`);
            const requiredFields = step.querySelectorAll('[required]');
            
            for (let field of requiredFields) {
                if (!field.value.trim()) {
                    field.focus();
                    alert('يرجى ملء جميع الحقول المطلوبة');
                    return false;
                }
            }
            
            return true;
        }

        function handleImageUpload(event) {
            const files = Array.from(event.target.files);
            
            files.forEach(file => {
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        uploadedImages.push({
                            file: file,
                            url: e.target.result,
                            isPrimary: uploadedImages.length === 0
                        });
                        updateImagePreview();
                    };
                    reader.readAsDataURL(file);
                }
            });
        }

        function updateImagePreview() {
            const preview = document.getElementById('imagePreview');
            preview.innerHTML = uploadedImages.map((image, index) => `
                <div class="image-preview-item">
                    <img src="${image.url}" alt="Product Image">
                    <button type="button" class="remove-btn" onclick="removeImage(${index})">
                        <i class="fas fa-times"></i>
                    </button>
                    ${image.isPrimary ? '<span class="primary-btn">رئيسية</span>' : 
                      `<button type="button" class="primary-btn" onclick="setPrimaryImage(${index})">جعل رئيسية</button>`}
                </div>
            `).join('');
        }

        function removeImage(index) {
            uploadedImages.splice(index, 1);
            if (uploadedImages.length > 0 && !uploadedImages.some(img => img.isPrimary)) {
                uploadedImages[0].isPrimary = true;
            }
            updateImagePreview();
        }

        function setPrimaryImage(index) {
            uploadedImages.forEach(img => img.isPrimary = false);
            uploadedImages[index].isPrimary = true;
            updateImagePreview();
        }

        function addTag(event) {
            if (event.key === 'Enter') {
                event.preventDefault();
                const input = event.target;
                const tag = input.value.trim();
                
                if (tag && !tags.includes(tag)) {
                    tags.push(tag);
                    input.value = '';
                    updateTagDisplay();
                }
            }
        }

        function updateTagDisplay() {
            const tagInput = document.getElementById('tagInput');
            const input = tagInput.querySelector('input');
            
            tagInput.innerHTML = tags.map(tag => `
                <span class="tag">
                    ${tag}
                    <span class="remove-tag" onclick="removeTag('${tag}')">&times;</span>
                </span>
            `).join('') + input.outerHTML;
            
            // Re-attach event listener
            tagInput.querySelector('input').addEventListener('keypress', addTag);
        }

        function removeTag(tagToRemove) {
            tags = tags.filter(tag => tag !== tagToRemove);
            updateTagDisplay();
        }

        function addVariant() {
            const variantId = Date.now();
            const variantHtml = `
                <div class="variant-row" id="variant-${variantId}">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" placeholder="مثل: اللون">
                                <label>اسم المتغير</label>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" placeholder="مثل: أحمر">
                                <label>القيمة</label>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-floating mb-3">
                                <input type="number" class="form-control" step="0.01" value="0">
                                <label>تعديل السعر</label>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-floating mb-3">
                                <input type="number" class="form-control" min="0" value="0">
                                <label>المخزون</label>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <button type="button" class="btn btn-outline-danger w-100" onclick="removeVariant(${variantId})">
                                <i class="fas fa-trash"></i> حذف
                            </button>
                        </div>
                    </div>
                </div>
            `;
            
            document.getElementById('variantsContainer').insertAdjacentHTML('beforeend', variantHtml);
        }

        function removeVariant(variantId) {
            document.getElementById(`variant-${variantId}`).remove();
        }

        function generateProductPreview() {
            const preview = document.getElementById('productPreview');
            const name = document.getElementById('productName').value;
            const price = document.getElementById('price').value;
            const description = document.getElementById('description').value;
            const primaryImage = uploadedImages.find(img => img.isPrimary);
            
            preview.innerHTML = `
                <div class="card">
                    <div class="row g-0">
                        <div class="col-md-4">
                            <img src="${primaryImage ? primaryImage.url : '/static/images/no-image.png'}" 
                                 class="img-fluid rounded-start" alt="${name}">
                        </div>
                        <div class="col-md-8">
                            <div class="card-body">
                                <h5 class="card-title">${name}</h5>
                                <p class="card-text">${description.substring(0, 150)}...</p>
                                <p class="card-text">
                                    <strong class="text-primary fs-4">${price} ر.س</strong>
                                </p>
                                <p class="card-text">
                                    <small class="text-muted">العلامات: ${tags.join(', ')}</small>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // Form submission
        document.getElementById('productForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData();
            
            // Add form fields
            formData.append('name', document.getElementById('productName').value);
            formData.append('category', document.getElementById('category').value);
            formData.append('description', document.getElementById('description').value);
            formData.append('short_description', document.getElementById('shortDescription').value);
            formData.append('sku', document.getElementById('sku').value);
            formData.append('price', document.getElementById('price').value);
            formData.append('stock_quantity', document.getElementById('stockQuantity').value);
            formData.append('status', document.getElementById('status').value);
            formData.append('tags', JSON.stringify(tags));
            
            // Add images
            uploadedImages.forEach((image, index) => {
                formData.append(`image_${index}`, image.file);
                formData.append(`image_${index}_primary`, image.isPrimary);
            });
            
            // Simulate API call
            const submitBtn = document.getElementById('submitBtn');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
            submitBtn.disabled = true;
            
            setTimeout(() => {
                alert('تم حفظ المنتج بنجاح!');
                window.location.href = '/vendor/products/';
            }, 2000);
        });

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateStepDisplay();
        });
    </script>
</body>
</html>
