from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from .models import ProductCategory, Product, ProductImage, ProductVariant, ProductReview


class ProductImageInline(admin.TabularInline):
    model = ProductImage
    extra = 1
    fields = ('image', 'alt_text', 'is_primary', 'sort_order')


class ProductVariantInline(admin.TabularInline):
    model = ProductVariant
    extra = 0
    fields = ('name', 'value', 'price_adjustment', 'stock_quantity', 'sku', 'is_active')


@admin.register(ProductCategory)
class ProductCategoryAdmin(admin.ModelAdmin):
    list_display = ('name', 'parent', 'is_active', 'sort_order', 'created_at')
    list_filter = ('is_active', 'parent', 'created_at')
    search_fields = ('name', 'description')
    prepopulated_fields = {'slug': ('name',)}
    ordering = ('sort_order', 'name')
    list_editable = ('sort_order', 'is_active')


@admin.register(Product)
class ProductAdmin(admin.ModelAdmin):
    list_display = ('name', 'store', 'category', 'price', 'stock_quantity', 'status', 'is_featured', 'created_at')
    list_filter = ('status', 'category', 'store', 'is_featured', 'is_digital', 'created_at')
    search_fields = ('name', 'description', 'sku', 'store__name')
    prepopulated_fields = {'slug': ('name',)}
    ordering = ('-created_at',)
    readonly_fields = ('rating', 'total_sales', 'views_count', 'created_at', 'updated_at')
    inlines = [ProductImageInline, ProductVariantInline]

    fieldsets = (
        (_('معلومات أساسية'), {
            'fields': ('store', 'name', 'slug', 'description', 'short_description', 'category')
        }),
        (_('التسعير'), {
            'fields': ('price', 'compare_price', 'cost_price')
        }),
        (_('المخزون'), {
            'fields': ('sku', 'barcode', 'stock_quantity', 'low_stock_threshold')
        }),
        (_('الشحن'), {
            'fields': ('weight', 'dimensions', 'requires_shipping')
        }),
        (_('إعدادات المنتج'), {
            'fields': ('status', 'is_featured', 'is_digital', 'tags')
        }),
        (_('SEO'), {
            'fields': ('meta_title', 'meta_description'),
            'classes': ('collapse',)
        }),
        (_('إحصائيات'), {
            'fields': ('rating', 'total_sales', 'views_count', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('store', 'category')


@admin.register(ProductReview)
class ProductReviewAdmin(admin.ModelAdmin):
    list_display = ('product', 'user', 'rating', 'title', 'is_verified_purchase', 'is_approved', 'created_at')
    list_filter = ('rating', 'is_verified_purchase', 'is_approved', 'created_at')
    search_fields = ('product__name', 'user__username', 'title', 'comment')
    ordering = ('-created_at',)
    readonly_fields = ('helpful_count', 'created_at', 'updated_at')

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('product', 'user')
