# Generated by Django 5.2.4 on 2025-07-16 23:02

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='StoreCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='اسم التصنيف')),
                ('slug', models.SlugField(blank=True, max_length=100, unique=True, verbose_name='الرابط المختصر')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('icon', models.CharField(blank=True, max_length=50, null=True, verbose_name='أيقونة')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
            ],
            options={
                'verbose_name': 'تصنيف المتجر',
                'verbose_name_plural': 'تصنيفات المتاجر',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Store',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم المتجر')),
                ('slug', models.SlugField(blank=True, max_length=200, unique=True, verbose_name='الرابط المختصر')),
                ('description', models.TextField(verbose_name='وصف المتجر')),
                ('logo', models.ImageField(blank=True, null=True, upload_to='store_logos/', verbose_name='شعار المتجر')),
                ('banner', models.ImageField(blank=True, null=True, upload_to='store_banners/', verbose_name='بانر المتجر')),
                ('address', models.TextField(verbose_name='عنوان المتجر')),
                ('phone', models.CharField(max_length=20, verbose_name='رقم الهاتف')),
                ('email', models.EmailField(max_length=254, verbose_name='البريد الإلكتروني')),
                ('website', models.URLField(blank=True, null=True, verbose_name='الموقع الإلكتروني')),
                ('status', models.CharField(choices=[('pending', 'في انتظار الموافقة'), ('approved', 'مُوافق عليه'), ('suspended', 'مُعلق'), ('rejected', 'مرفوض')], default='pending', max_length=20, verbose_name='حالة المتجر')),
                ('commission_rate', models.DecimalField(decimal_places=4, default=0.05, max_digits=5, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(1)], verbose_name='معدل العمولة')),
                ('is_featured', models.BooleanField(default=False, verbose_name='متجر مميز')),
                ('rating', models.DecimalField(decimal_places=2, default=0.0, max_digits=3, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(5)], verbose_name='التقييم')),
                ('total_sales', models.DecimalField(decimal_places=2, default=0.0, max_digits=12, verbose_name='إجمالي المبيعات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('owner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='stores', to=settings.AUTH_USER_MODEL, verbose_name='صاحب المتجر')),
                ('category', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='stores', to='stores.storecategory', verbose_name='تصنيف المتجر')),
            ],
            options={
                'verbose_name': 'متجر',
                'verbose_name_plural': 'المتاجر',
                'ordering': ['-created_at'],
            },
        ),
    ]
