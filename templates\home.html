<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>السوق الإلكتروني - الصفحة الرئيسية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="/static/css/style.css" rel="stylesheet">
    <link href="/static/css/responsive.css" rel="stylesheet">
    <link href="/static/css/animations.css" rel="stylesheet">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

        body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        .navbar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.8rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-link {
            font-weight: 500;
            color: #2c3e50 !important;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-link:hover {
            color: #667eea !important;
            transform: translateY(-2px);
        }

        .nav-link::after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: 0;
            left: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            transition: all 0.3s ease;
        }

        .nav-link:hover::after {
            width: 100%;
            left: 0;
        }

        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 120px 0;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
            animation: float 20s infinite linear;
        }

        @keyframes float {
            0% { transform: translateX(0) translateY(0); }
            100% { transform: translateX(-50px) translateY(-50px); }
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .hero-subtitle {
            font-size: 1.3rem;
            font-weight: 300;
            margin-bottom: 2rem;
            opacity: 0.9;
        }

        .btn-hero {
            padding: 15px 30px;
            font-size: 1.1rem;
            font-weight: 600;
            border-radius: 50px;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            position: relative;
            overflow: hidden;
        }

        .btn-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn-hero:hover::before {
            left: 100%;
        }

        .btn-hero:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 2rem;
            transition: all 0.4s ease;
            height: 100%;
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .feature-card:hover::before {
            transform: scaleX(1);
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            background: rgba(255, 255, 255, 1);
        }

        .feature-icon {
            font-size: 3.5rem;
            margin-bottom: 1.5rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            transition: all 0.3s ease;
        }

        .feature-card:hover .feature-icon {
            transform: scale(1.1);
        }

        .product-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: none;
            border-radius: 20px;
            overflow: hidden;
            transition: all 0.4s ease;
            height: 100%;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .product-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
        }

        .product-image {
            height: 220px;
            object-fit: cover;
            width: 100%;
            transition: transform 0.3s ease;
        }

        .product-card:hover .product-image {
            transform: scale(1.05);
        }

        .stats-section {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            margin: 2rem 0;
            padding: 2rem;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        .section-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 1rem;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .section-subtitle {
            font-size: 1.2rem;
            color: #7f8c8d;
            font-weight: 300;
        }

        .footer {
            background: linear-gradient(135deg, #2c3e50, #34495e);
            color: white;
            padding: 3rem 0;
            margin-top: 4rem;
        }

        .badge-custom {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 500;
        }

        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }

            .hero-subtitle {
                font-size: 1.1rem;
            }

            .btn-hero {
                padding: 12px 25px;
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg fixed-top">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-store"></i> السوق الإلكتروني
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/stores/">المتاجر</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/products/">المنتجات</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="/cart/">
                            <i class="fas fa-shopping-cart"></i> السلة <span class="badge bg-primary">0</span>
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> حسابي
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/login/">تسجيل الدخول</a></li>
                            <li><a class="dropdown-item" href="/register/">إنشاء حساب</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/profile/">الملف الشخصي</a></li>
                            <li><a class="dropdown-item" href="/orders/">طلباتي</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/admin/">لوحة التحكم</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section text-center">
        <div class="container hero-content">
            <h1 class="hero-title">مرحباً بك في السوق الإلكتروني</h1>
            <p class="hero-subtitle">منصة شاملة تجمع بين المتاجر والعملاء مع نظام عمولات متقدم</p>
            <div class="d-flex justify-content-center gap-3 flex-wrap">
                <a href="/products/" class="btn btn-light btn-hero">
                    <i class="fas fa-shopping-bag me-2"></i> تصفح المنتجات
                </a>
                <a href="/stores/" class="btn btn-outline-light btn-hero">
                    <i class="fas fa-store me-2"></i> المتاجر
                </a>
                <a href="/add-store/" class="btn btn-success btn-hero">
                    <i class="fas fa-plus me-2"></i> أنشئ متجرك
                </a>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="py-5" style="margin-top: 80px;">
        <div class="container">
            <div class="row text-center mb-5">
                <div class="col-12">
                    <h2 class="section-title">مميزات المنصة</h2>
                    <p class="section-subtitle">نظام متكامل لإدارة السوق الإلكتروني</p>
                </div>
            </div>
            <div class="row g-4">
                <div class="col-md-4 mb-4 animate-on-scroll">
                    <div class="feature-card text-center card-modern hover-lift">
                        <i class="fas fa-store feature-icon float-animation"></i>
                        <h5 class="fw-bold mb-3">إدارة المتاجر</h5>
                        <p class="text-muted">نظام شامل لإدارة المتاجر والموافقة عليها وتتبع أدائها</p>
                    </div>
                </div>
                <div class="col-md-4 mb-4 animate-on-scroll">
                    <div class="feature-card text-center card-modern hover-lift">
                        <i class="fas fa-box feature-icon pulse-animation"></i>
                        <h5 class="fw-bold mb-3">إدارة المنتجات</h5>
                        <p class="text-muted">إضافة وإدارة المنتجات مع صور متعددة ومتغيرات مختلفة</p>
                    </div>
                </div>
                <div class="col-md-4 mb-4 animate-on-scroll">
                    <div class="feature-card text-center card-modern hover-lift">
                        <i class="fas fa-credit-card feature-icon glow-animation"></i>
                        <h5 class="fw-bold mb-3">نظام الدفع والعمولات</h5>
                        <p class="text-muted">نظام دفع متقدم مع حساب العمولات تلقائياً وتوزيع الأرباح</p>
                    </div>
                </div>
                <div class="col-md-4 mb-4 animate-on-scroll">
                    <div class="feature-card text-center card-modern hover-lift">
                        <i class="fas fa-shopping-cart feature-icon bounce-in"></i>
                        <h5 class="fw-bold mb-3">سلة المشتريات</h5>
                        <p class="text-muted">سلة مشتريات ذكية مع حساب العمولات والضرائب</p>
                    </div>
                </div>
                <div class="col-md-4 mb-4 animate-on-scroll">
                    <div class="feature-card text-center card-modern hover-lift">
                        <i class="fas fa-truck feature-icon float-animation"></i>
                        <h5 class="fw-bold mb-3">تتبع الطلبات</h5>
                        <p class="text-muted">نظام متقدم لتتبع الطلبات وإدارة حالات الشحن</p>
                    </div>
                </div>
                <div class="col-md-4 mb-4 animate-on-scroll">
                    <div class="feature-card text-center card-modern hover-lift">
                        <i class="fas fa-chart-bar feature-icon pulse-animation"></i>
                        <h5 class="fw-bold mb-3">التقارير والإحصائيات</h5>
                        <p class="text-muted">تقارير شاملة للمبيعات والعمولات والأداء</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Products Section -->
    <section class="py-5">
        <div class="container">
            <div class="row text-center mb-5">
                <div class="col-12">
                    <h2 class="section-title">المنتجات المميزة</h2>
                    <p class="section-subtitle">اكتشف أفضل المنتجات من متاجرنا المختارة</p>
                </div>
            </div>
            <div class="row g-4">
                <div class="col-lg-3 col-md-6 animate-on-scroll">
                    <div class="product-card card-modern">
                        <div class="position-relative">
                            <img src="/static/images/iphone15.jpg" class="product-image" alt="iPhone 15 Pro">
                            <div class="position-absolute top-0 end-0 m-2">
                                <span class="badge badge-modern">جديد</span>
                            </div>
                            <div class="position-absolute top-0 start-0 m-2">
                                <button class="btn btn-outline-light btn-sm rounded-circle" onclick="toggleWishlist(this)">
                                    <i class="far fa-heart"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-body p-3">
                            <h6 class="card-title fw-bold mb-2">iPhone 15 Pro</h6>
                            <p class="card-text text-muted small mb-2">
                                <i class="fas fa-store me-1"></i> متجر التقنية الحديثة
                            </p>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="price">4,999 ر.س</span>
                                <div class="rating">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <small class="text-muted ms-1">(4.9)</small>
                                </div>
                            </div>
                            <div class="d-grid gap-2">
                                <button class="btn btn-add-cart btn-modern btn-primary-modern btn-sm">
                                    <i class="fas fa-cart-plus me-1"></i> أضف للسلة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="product-card">
                        <div class="position-relative">
                            <img src="/static/images/samsung-s24.jpg" class="product-image" alt="Samsung Galaxy S24">
                            <div class="position-absolute top-0 end-0 m-2">
                                <span class="badge bg-warning text-dark">خصم 10%</span>
                            </div>
                        </div>
                        <div class="card-body p-3">
                            <h6 class="card-title fw-bold mb-2">Samsung Galaxy S24 Ultra</h6>
                            <p class="card-text text-muted small mb-2">
                                <i class="fas fa-store me-1"></i> متجر التقنية الحديثة
                            </p>
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <span class="text-primary fw-bold fs-5">4,299 ر.س</span>
                                    <small class="text-muted text-decoration-line-through ms-1">4,799 ر.س</small>
                                </div>
                                <div class="text-warning">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="far fa-star"></i>
                                </div>
                            </div>
                            <button class="btn btn-primary w-100 mt-2 btn-sm">
                                <i class="fas fa-cart-plus me-1"></i> أضف للسلة
                            </button>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="product-card">
                        <div class="position-relative">
                            <img src="/static/images/airpods.jpg" class="product-image" alt="AirPods Pro">
                            <div class="position-absolute top-0 end-0 m-2">
                                <span class="badge bg-success">الأكثر مبيعاً</span>
                            </div>
                        </div>
                        <div class="card-body p-3">
                            <h6 class="card-title fw-bold mb-2">AirPods Pro 3</h6>
                            <p class="card-text text-muted small mb-2">
                                <i class="fas fa-store me-1"></i> متجر التقنية الحديثة
                            </p>
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="text-primary fw-bold fs-5">899 ر.س</span>
                                <div class="text-warning">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                </div>
                            </div>
                            <button class="btn btn-primary w-100 mt-2 btn-sm">
                                <i class="fas fa-cart-plus me-1"></i> أضف للسلة
                            </button>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="product-card">
                        <div class="position-relative">
                            <img src="/static/images/macbook.jpg" class="product-image" alt="MacBook Pro">
                            <div class="position-absolute top-0 end-0 m-2">
                                <span class="badge bg-info">مميز</span>
                            </div>
                        </div>
                        <div class="card-body p-3">
                            <h6 class="card-title fw-bold mb-2">MacBook Pro M3</h6>
                            <p class="card-text text-muted small mb-2">
                                <i class="fas fa-store me-1"></i> متجر التقنية الحديثة
                            </p>
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="text-primary fw-bold fs-5">8,999 ر.س</span>
                                <div class="text-warning">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                </div>
                            </div>
                            <button class="btn btn-primary w-100 mt-2 btn-sm">
                                <i class="fas fa-cart-plus me-1"></i> أضف للسلة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="text-center mt-5">
                <a href="/products/" class="btn btn-primary btn-hero btn-lg">
                    <i class="fas fa-eye me-2"></i> عرض جميع المنتجات
                </a>
            </div>
        </div>
    </section>

    <!-- Statistics Section -->
    <section class="py-5">
        <div class="container">
            <div class="stats-section">
                <div class="row text-center">
                    <div class="col-md-3 mb-3">
                        <div class="stat-card">
                            <h3 class="fw-bold mb-2">1,250+</h3>
                            <p class="mb-0">منتج متاح</p>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="stat-card">
                            <h3 class="fw-bold mb-2">150+</h3>
                            <p class="mb-0">متجر مُفعل</p>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="stat-card">
                            <h3 class="fw-bold mb-2">5,000+</h3>
                            <p class="mb-0">عميل راضي</p>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="stat-card">
                            <h3 class="fw-bold mb-2">99.9%</h3>
                            <p class="mb-0">وقت التشغيل</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Call to Action Section -->
    <section class="py-5">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="feature-card text-center p-5">
                        <h2 class="section-title mb-4">ابدأ رحلتك معنا اليوم</h2>
                        <p class="section-subtitle mb-4">انضم إلى آلاف التجار الناجحين واكتشف الفرص اللامحدودة</p>
                        <div class="d-flex justify-content-center gap-3 flex-wrap">
                            <a href="/register/" class="btn btn-primary btn-hero btn-lg">
                                <i class="fas fa-user-plus me-2"></i> إنشاء حساب جديد
                            </a>
                            <a href="/add-store/" class="btn btn-success btn-hero btn-lg">
                                <i class="fas fa-store me-2"></i> أنشئ متجرك الآن
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <h5 class="fw-bold mb-3">السوق الإلكتروني</h5>
                    <p class="text-light opacity-75">منصة شاملة تجمع بين المتاجر والعملاء مع نظام عمولات متقدم وتجربة تسوق استثنائية.</p>
                    <div class="d-flex gap-3">
                        <a href="#" class="text-light"><i class="fab fa-facebook fa-lg"></i></a>
                        <a href="#" class="text-light"><i class="fab fa-twitter fa-lg"></i></a>
                        <a href="#" class="text-light"><i class="fab fa-instagram fa-lg"></i></a>
                        <a href="#" class="text-light"><i class="fab fa-linkedin fa-lg"></i></a>
                    </div>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="fw-bold mb-3">روابط سريعة</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="/products/" class="text-light opacity-75 text-decoration-none">المنتجات</a></li>
                        <li class="mb-2"><a href="/stores/" class="text-light opacity-75 text-decoration-none">المتاجر</a></li>
                        <li class="mb-2"><a href="/cart/" class="text-light opacity-75 text-decoration-none">السلة</a></li>
                        <li class="mb-2"><a href="/orders/" class="text-light opacity-75 text-decoration-none">الطلبات</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="fw-bold mb-3">للمتاجر</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="/add-store/" class="text-light opacity-75 text-decoration-none">إنشاء متجر</a></li>
                        <li class="mb-2"><a href="/add-product/" class="text-light opacity-75 text-decoration-none">إضافة منتج</a></li>
                        <li class="mb-2"><a href="/admin/" class="text-light opacity-75 text-decoration-none">لوحة التحكم</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="fw-bold mb-3">الدعم</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="#" class="text-light opacity-75 text-decoration-none">مركز المساعدة</a></li>
                        <li class="mb-2"><a href="#" class="text-light opacity-75 text-decoration-none">اتصل بنا</a></li>
                        <li class="mb-2"><a href="#" class="text-light opacity-75 text-decoration-none">الشروط والأحكام</a></li>
                        <li class="mb-2"><a href="#" class="text-light opacity-75 text-decoration-none">سياسة الخصوصية</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="fw-bold mb-3">تواصل معنا</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2 text-light opacity-75"><i class="fas fa-phone me-2"></i> +966 50 123 4567</li>
                        <li class="mb-2 text-light opacity-75"><i class="fas fa-envelope me-2"></i> <EMAIL></li>
                        <li class="mb-2 text-light opacity-75"><i class="fas fa-map-marker-alt me-2"></i> الرياض، المملكة العربية السعودية</li>
                    </ul>
                </div>
            </div>
            <hr class="my-4 opacity-25">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0 text-light opacity-75">&copy; 2024 السوق الإلكتروني. جميع الحقوق محفوظة.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0 text-light opacity-75">صُنع بـ ❤️ في المملكة العربية السعودية</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/main.js"></script>
    <script>
        // Page-specific JavaScript
        function toggleWishlist(button) {
            const icon = button.querySelector('i');
            if (icon.classList.contains('far')) {
                icon.classList.remove('far');
                icon.classList.add('fas');
                button.classList.remove('btn-outline-light');
                button.classList.add('btn-danger');
                MarketplaceApp.showNotification('تم إضافة المنتج للمفضلة', 'success');
            } else {
                icon.classList.remove('fas');
                icon.classList.add('far');
                button.classList.remove('btn-danger');
                button.classList.add('btn-outline-light');
                MarketplaceApp.showNotification('تم إزالة المنتج من المفضلة', 'info');
            }
        }

        // Initialize page animations
        document.addEventListener('DOMContentLoaded', function() {
            // Add scroll animations
            const animatedElements = document.querySelectorAll('.animate-on-scroll');
            animatedElements.forEach((el, index) => {
                el.style.animationDelay = `${index * 0.1}s`;
            });

            // Initialize lazy loading for images
            if ('IntersectionObserver' in window) {
                const imageObserver = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const img = entry.target;
                            if (img.dataset.src) {
                                img.src = img.dataset.src;
                                img.classList.remove('lazy');
                                imageObserver.unobserve(img);
                            }
                        }
                    });
                });

                document.querySelectorAll('img[data-src]').forEach(img => {
                    imageObserver.observe(img);
                });
            }

            // Add smooth scroll behavior
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        });
    </script>
</body>
</html>
