version: '3.8'

services:
  # قاعدة البيانات PostgreSQL
  db:
    image: postgres:15
    volumes:
      - postgres_data:/var/lib/postgresql/data/
    environment:
      POSTGRES_DB: marketplace_db
      POSTGRES_USER: marketplace_user
      POSTGRES_PASSWORD: marketplace_password
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U marketplace_user -d marketplace_db"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis للتخزين المؤقت والمهام
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # تطبيق Django
  web:
    build: .
    command: gunicorn --bind 0.0.0.0:8000 marketplace.wsgi:application
    volumes:
      - .:/app
      - static_volume:/app/staticfiles
      - media_volume:/app/media
    ports:
      - "8000:8000"
    environment:
      - DEBUG=False
      - SECRET_KEY=your-production-secret-key
      - DB_NAME=marketplace_db
      - DB_USER=marketplace_user
      - DB_PASSWORD=marketplace_password
      - DB_HOST=db
      - DB_PORT=5432
      - REDIS_URL=redis://redis:6379/1
      - CELERY_BROKER_URL=redis://redis:6379/0
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/admin/"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Celery Worker للمهام الخلفية
  celery:
    build: .
    command: celery -A marketplace worker --loglevel=info
    volumes:
      - .:/app
    environment:
      - DEBUG=False
      - SECRET_KEY=your-production-secret-key
      - DB_NAME=marketplace_db
      - DB_USER=marketplace_user
      - DB_PASSWORD=marketplace_password
      - DB_HOST=db
      - DB_PORT=5432
      - CELERY_BROKER_URL=redis://redis:6379/0
    depends_on:
      - db
      - redis

  # Celery Beat للمهام المجدولة
  celery-beat:
    build: .
    command: celery -A marketplace beat --loglevel=info
    volumes:
      - .:/app
    environment:
      - DEBUG=False
      - SECRET_KEY=your-production-secret-key
      - DB_NAME=marketplace_db
      - DB_USER=marketplace_user
      - DB_PASSWORD=marketplace_password
      - DB_HOST=db
      - DB_PORT=5432
      - CELERY_BROKER_URL=redis://redis:6379/0
    depends_on:
      - db
      - redis

  # Nginx كخادم ويب عكسي
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - static_volume:/app/staticfiles
      - media_volume:/app/media
      - ./ssl:/etc/nginx/ssl  # للشهادات SSL
    depends_on:
      - web

volumes:
  postgres_data:
  static_volume:
  media_volume:
