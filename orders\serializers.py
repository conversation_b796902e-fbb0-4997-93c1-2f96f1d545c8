from rest_framework import serializers
from .models import Cart, CartItem, Order, OrderItem, OrderStatusHistory
from products.serializers import ProductListSerializer, ProductVariantSerializer
from products.models import Product, ProductVariant


class CartItemSerializer(serializers.ModelSerializer):
    product = ProductListSerializer(read_only=True)
    variant = ProductVariantSerializer(read_only=True)
    product_id = serializers.IntegerField(write_only=True)
    variant_id = serializers.IntegerField(write_only=True, required=False, allow_null=True)
    
    class Meta:
        model = CartItem
        fields = [
            'id', 'product', 'variant', 'quantity', 'price', 'total_price', 
            'commission_amount', 'product_id', 'variant_id', 'created_at'
        ]
        read_only_fields = ['price', 'total_price', 'commission_amount', 'created_at']
    
    def create(self, validated_data):
        product_id = validated_data.pop('product_id')
        variant_id = validated_data.pop('variant_id', None)
        
        try:
            product = Product.objects.get(id=product_id, status='active')
        except Product.DoesNotExist:
            raise serializers.ValidationError("المنتج غير موجود أو غير نشط")
        
        variant = None
        if variant_id:
            try:
                variant = ProductVariant.objects.get(id=variant_id, product=product, is_active=True)
            except ProductVariant.DoesNotExist:
                raise serializers.ValidationError("متغير المنتج غير موجود أو غير نشط")
        
        validated_data['product'] = product
        validated_data['variant'] = variant
        
        return super().create(validated_data)
    
    def validate_quantity(self, value):
        if value <= 0:
            raise serializers.ValidationError("الكمية يجب أن تكون أكبر من صفر")
        return value


class CartSerializer(serializers.ModelSerializer):
    items = CartItemSerializer(many=True, read_only=True)
    
    class Meta:
        model = Cart
        fields = [
            'id', 'total_items', 'subtotal', 'total_commission', 'total_price', 
            'items', 'created_at', 'updated_at'
        ]
        read_only_fields = ['total_items', 'subtotal', 'total_commission', 'total_price', 'created_at', 'updated_at']


class OrderItemSerializer(serializers.ModelSerializer):
    class Meta:
        model = OrderItem
        fields = [
            'id', 'product_name', 'product_sku', 'store_name', 'quantity', 
            'price', 'total_price', 'commission_amount', 'store_amount'
        ]


class OrderStatusHistorySerializer(serializers.ModelSerializer):
    changed_by_name = serializers.CharField(source='changed_by.username', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = OrderStatusHistory
        fields = ['id', 'status', 'status_display', 'notes', 'changed_by_name', 'created_at']


class OrderListSerializer(serializers.ModelSerializer):
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    items_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Order
        fields = [
            'id', 'order_number', 'status', 'status_display', 'total_amount', 
            'total_commission', 'items_count', 'created_at'
        ]
    
    def get_items_count(self, obj):
        return obj.items.count()


class OrderDetailSerializer(serializers.ModelSerializer):
    items = OrderItemSerializer(many=True, read_only=True)
    status_history = OrderStatusHistorySerializer(many=True, read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = Order
        fields = [
            'id', 'order_number', 'status', 'status_display', 'billing_first_name',
            'billing_last_name', 'billing_email', 'billing_phone', 'billing_address',
            'billing_city', 'billing_postal_code', 'billing_country', 'shipping_first_name',
            'shipping_last_name', 'shipping_phone', 'shipping_address', 'shipping_city',
            'shipping_postal_code', 'shipping_country', 'subtotal', 'shipping_cost',
            'tax_amount', 'total_commission', 'total_amount', 'notes', 'tracking_number',
            'items', 'status_history', 'created_at', 'updated_at', 'shipped_at', 'delivered_at'
        ]


class OrderCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Order
        fields = [
            'billing_first_name', 'billing_last_name', 'billing_email', 'billing_phone',
            'billing_address', 'billing_city', 'billing_postal_code', 'billing_country',
            'shipping_first_name', 'shipping_last_name', 'shipping_phone', 'shipping_address',
            'shipping_city', 'shipping_postal_code', 'shipping_country', 'notes'
        ]
    
    def create(self, validated_data):
        request = self.context.get('request')
        user = request.user
        
        # Get user's cart
        try:
            cart = Cart.objects.get(user=user)
        except Cart.DoesNotExist:
            raise serializers.ValidationError("سلة المشتريات فارغة")
        
        if not cart.items.exists():
            raise serializers.ValidationError("سلة المشتريات فارغة")
        
        # Calculate totals
        subtotal = cart.subtotal
        total_commission = cart.total_commission
        shipping_cost = 0  # يمكن حسابها لاحقاً
        tax_amount = 0     # يمكن حسابها لاحقاً
        total_amount = subtotal + shipping_cost + tax_amount
        
        # Create order
        order = Order.objects.create(
            user=user,
            subtotal=subtotal,
            shipping_cost=shipping_cost,
            tax_amount=tax_amount,
            total_commission=total_commission,
            total_amount=total_amount,
            **validated_data
        )
        
        # Create order items from cart items
        for cart_item in cart.items.all():
            OrderItem.objects.create(
                order=order,
                product=cart_item.product,
                variant=cart_item.variant,
                quantity=cart_item.quantity,
                price=cart_item.price,
                commission_rate=cart_item.commission_rate,
                commission_amount=cart_item.commission_amount,
                store_name=cart_item.product.store.name,
                product_name=cart_item.product.name,
                product_sku=cart_item.product.sku
            )
        
        # Clear cart
        cart.items.all().delete()
        
        # Create initial status history
        OrderStatusHistory.objects.create(
            order=order,
            status='pending',
            notes='تم إنشاء الطلب',
            changed_by=user
        )
        
        return order
