#!/usr/bin/env python
"""
اختبار شامل للنظام
Comprehensive system testing
"""
import os
import django
import requests
import json
from decimal import Decimal

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'marketplace.settings')
django.setup()

from accounts.models import User
from stores.models import Store
from products.models import Product
from orders.models import Order, Cart
from payments.models import Payment

def test_models():
    """اختبار النماذج"""
    print("🧪 اختبار النماذج...")
    
    # اختبار المستخدمين
    users_count = User.objects.count()
    print(f"✅ المستخدمون: {users_count}")
    
    # اختبار المتاجر
    stores_count = Store.objects.count()
    approved_stores = Store.objects.filter(status='approved').count()
    print(f"✅ المتاجر: {stores_count} (مُوافق عليها: {approved_stores})")
    
    # اختبار المنتجات
    products_count = Product.objects.count()
    active_products = Product.objects.filter(status='active').count()
    print(f"✅ المنتجات: {products_count} (نشطة: {active_products})")
    
    # اختبار الطلبات
    orders_count = Order.objects.count()
    print(f"✅ الطلبات: {orders_count}")
    
    return True

def test_api_endpoints():
    """اختبار نقاط API"""
    print("\n🌐 اختبار نقاط API...")
    
    base_url = "http://127.0.0.1:8000"
    
    # اختبار الصفحة الرئيسية
    try:
        response = requests.get(f"{base_url}/")
        if response.status_code == 200:
            print("✅ الصفحة الرئيسية تعمل")
        else:
            print(f"❌ خطأ في الصفحة الرئيسية: {response.status_code}")
    except requests.exceptions.ConnectionError:
        print("❌ لا يمكن الاتصال بالخادم. تأكد من تشغيل الخادم أولاً")
        return False
    
    # اختبار API المنتجات
    try:
        response = requests.get(f"{base_url}/api/products/")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API المنتجات يعمل ({len(data.get('results', []))} منتج)")
        else:
            print(f"❌ خطأ في API المنتجات: {response.status_code}")
    except Exception as e:
        print(f"❌ خطأ في API المنتجات: {e}")
    
    # اختبار API التصنيفات
    try:
        response = requests.get(f"{base_url}/api/products/categories/")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API التصنيفات يعمل ({len(data)} تصنيف)")
        else:
            print(f"❌ خطأ في API التصنيفات: {response.status_code}")
    except Exception as e:
        print(f"❌ خطأ في API التصنيفات: {e}")
    
    # اختبار لوحة التحكم
    try:
        response = requests.get(f"{base_url}/admin/")
        if response.status_code == 200:
            print("✅ لوحة التحكم تعمل")
        else:
            print(f"❌ خطأ في لوحة التحكم: {response.status_code}")
    except Exception as e:
        print(f"❌ خطأ في لوحة التحكم: {e}")
    
    return True

def test_business_logic():
    """اختبار المنطق التجاري"""
    print("\n💼 اختبار المنطق التجاري...")
    
    # اختبار حساب العمولة
    try:
        store = Store.objects.filter(status='approved').first()
        if store:
            commission_rate = store.commission_rate
            test_amount = Decimal('100.00')
            expected_commission = test_amount * commission_rate
            print(f"✅ حساب العمولة: {test_amount} × {commission_rate} = {expected_commission}")
        else:
            print("⚠️  لا توجد متاجر مُوافق عليها لاختبار العمولة")
    except Exception as e:
        print(f"❌ خطأ في حساب العمولة: {e}")
    
    # اختبار حالات الطلب
    try:
        from orders.models import Order
        status_choices = dict(Order.STATUS_CHOICES)
        print(f"✅ حالات الطلب المتاحة: {len(status_choices)}")
        for key, value in status_choices.items():
            print(f"   • {key}: {value}")
    except Exception as e:
        print(f"❌ خطأ في حالات الطلب: {e}")
    
    return True

def test_permissions():
    """اختبار الصلاحيات"""
    print("\n🔐 اختبار الصلاحيات...")
    
    # اختبار أنواع المستخدمين
    try:
        customers = User.objects.filter(user_type='customer').count()
        vendors = User.objects.filter(user_type='vendor').count()
        admins = User.objects.filter(user_type='admin').count()
        
        print(f"✅ العملاء: {customers}")
        print(f"✅ أصحاب المتاجر: {vendors}")
        print(f"✅ المدراء: {admins}")
        
        # اختبار صلاحيات المتاجر
        vendor = User.objects.filter(user_type='vendor').first()
        if vendor:
            vendor_stores = vendor.stores.count()
            print(f"✅ متاجر المستخدم {vendor.username}: {vendor_stores}")
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الصلاحيات: {e}")
    
    return True

def test_data_integrity():
    """اختبار سلامة البيانات"""
    print("\n🔍 اختبار سلامة البيانات...")
    
    # اختبار العلاقات
    try:
        # المنتجات والمتاجر
        products_without_store = Product.objects.filter(store__isnull=True).count()
        if products_without_store == 0:
            print("✅ جميع المنتجات مرتبطة بمتاجر")
        else:
            print(f"⚠️  {products_without_store} منتج غير مرتبط بمتجر")
        
        # المتاجر والمالكين
        stores_without_owner = Store.objects.filter(owner__isnull=True).count()
        if stores_without_owner == 0:
            print("✅ جميع المتاجر لها مالكين")
        else:
            print(f"⚠️  {stores_without_owner} متجر بدون مالك")
        
        # المنتجات والأسعار
        products_with_zero_price = Product.objects.filter(price=0).count()
        if products_with_zero_price == 0:
            print("✅ جميع المنتجات لها أسعار صحيحة")
        else:
            print(f"⚠️  {products_with_zero_price} منتج بسعر صفر")
        
    except Exception as e:
        print(f"❌ خطأ في اختبار سلامة البيانات: {e}")
    
    return True

def generate_test_report():
    """إنشاء تقرير الاختبار"""
    print("\n📊 تقرير الاختبار الشامل")
    print("=" * 50)
    
    # إحصائيات عامة
    total_users = User.objects.count()
    total_stores = Store.objects.count()
    total_products = Product.objects.count()
    total_orders = Order.objects.count()
    
    print(f"👥 إجمالي المستخدمين: {total_users}")
    print(f"🏪 إجمالي المتاجر: {total_stores}")
    print(f"📦 إجمالي المنتجات: {total_products}")
    print(f"📋 إجمالي الطلبات: {total_orders}")
    
    # حالة النظام
    print("\n🚦 حالة النظام:")
    
    if total_users > 0:
        print("✅ نظام المستخدمين جاهز")
    else:
        print("⚠️  نظام المستخدمين يحتاج بيانات")
    
    if total_stores > 0:
        print("✅ نظام المتاجر جاهز")
    else:
        print("⚠️  نظام المتاجر يحتاج بيانات")
    
    if total_products > 0:
        print("✅ نظام المنتجات جاهز")
    else:
        print("⚠️  نظام المنتجات يحتاج بيانات")
    
    # توصيات
    print("\n💡 التوصيات:")
    
    if total_users < 5:
        print("• أضف المزيد من المستخدمين التجريبيين")
    
    if total_products < 10:
        print("• أضف المزيد من المنتجات التجريبية")
    
    if total_orders == 0:
        print("• قم بإنشاء بعض الطلبات التجريبية")
    
    print("\n🎯 لإضافة بيانات تجريبية:")
    print("python create_sample_data.py")

def main():
    print("🧪 بدء الاختبار الشامل للنظام")
    print("=" * 50)
    
    tests = [
        ("اختبار النماذج", test_models),
        ("اختبار نقاط API", test_api_endpoints),
        ("اختبار المنطق التجاري", test_business_logic),
        ("اختبار الصلاحيات", test_permissions),
        ("اختبار سلامة البيانات", test_data_integrity)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed_tests += 1
        except Exception as e:
            print(f"❌ خطأ في {test_name}: {e}")
    
    print(f"\n📈 نتائج الاختبار: {passed_tests}/{total_tests} اختبار نجح")
    
    if passed_tests == total_tests:
        print("🎉 جميع الاختبارات نجحت!")
    else:
        print("⚠️  بعض الاختبارات فشلت، يرجى مراجعة الأخطاء")
    
    generate_test_report()

if __name__ == "__main__":
    main()
