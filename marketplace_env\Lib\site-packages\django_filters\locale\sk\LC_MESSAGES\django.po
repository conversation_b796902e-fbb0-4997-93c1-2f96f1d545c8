# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-02-10 11:07+0000\n"
"PO-Revision-Date: 2023-07-21 19:07+0000\n"
"Last-Translator: Milan Šalka <<EMAIL>>\n"
"Language-Team: Slovak <https://hosted.weblate.org/projects/django-filter/"
"django-filter/sk/>\n"
"Language: sk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n == 1 ? 0 : n % 1 == 0 && n "
">= 2 && n <= 4 ? 1 : n % 1 != 0 ? 2: 3);\n"
"X-Generator: Weblate 5.0-dev\n"
"X-Translated-Using: django-rosetta 0.8.1\n"

#: conf.py:16
msgid "date"
msgstr "dátum"

#: conf.py:17
msgid "year"
msgstr "rok"

#: conf.py:18
msgid "month"
msgstr "mesiac"

#: conf.py:19
msgid "day"
msgstr "deň"

#: conf.py:20
msgid "week day"
msgstr "deň týždňa"

#: conf.py:21
msgid "hour"
msgstr "hodina"

#: conf.py:22
msgid "minute"
msgstr "minúta"

#: conf.py:23
msgid "second"
msgstr "sekunda"

#: conf.py:27 conf.py:28
msgid "contains"
msgstr "obsahuje"

#: conf.py:29
msgid "is in"
msgstr "je v"

#: conf.py:30
msgid "is greater than"
msgstr "je vačší než"

#: conf.py:31
msgid "is greater than or equal to"
msgstr "je vačší alebo rovný ako"

#: conf.py:32
msgid "is less than"
msgstr "je menší než"

#: conf.py:33
msgid "is less than or equal to"
msgstr "je menší alebo rovný ako"

#: conf.py:34 conf.py:35
msgid "starts with"
msgstr "začína s"

#: conf.py:36 conf.py:37
msgid "ends with"
msgstr "končí s"

#: conf.py:38
msgid "is in range"
msgstr "je v rozsahu"

#: conf.py:39
msgid "is null"
msgstr "je nulová"

#: conf.py:40 conf.py:41
msgid "matches regex"
msgstr "spĺňa regex"

#: conf.py:42 conf.py:49
msgid "search"
msgstr "hľadať"

#: conf.py:44
msgid "is contained by"
msgstr "je obsiahnutý"

#: conf.py:45
msgid "overlaps"
msgstr "presahuje"

#: conf.py:46
msgid "has key"
msgstr "má kľúč"

#: conf.py:47
msgid "has keys"
msgstr "má kľúče"

#: conf.py:48
msgid "has any keys"
msgstr "má akékoľvek kľúče"

#: fields.py:94
msgid "Select a lookup."
msgstr "Vyberte vyhľadávanie."

#: fields.py:198
msgid "Range query expects two values."
msgstr "Rozsah očakáva dve hodnoty."

#: filters.py:437
msgid "Today"
msgstr "Dnes"

#: filters.py:438
msgid "Yesterday"
msgstr "Včera"

#: filters.py:439
msgid "Past 7 days"
msgstr "Posledných 7 dní"

#: filters.py:440
msgid "This month"
msgstr "Tento mesiac"

#: filters.py:441
msgid "This year"
msgstr "Tento rok"

#: filters.py:543
msgid "Multiple values may be separated by commas."
msgstr "Viacero hodnôt môže byť oddelených čiarkami."

#: filters.py:721
#, python-format
msgid "%s (descending)"
msgstr "%s (klesajúco)"

#: filters.py:737
msgid "Ordering"
msgstr "Zoradenie"

#: rest_framework/filterset.py:33
#: templates/django_filters/rest_framework/form.html:5
msgid "Submit"
msgstr "Potvrdiť"

#: templates/django_filters/rest_framework/crispy_form.html:4
#: templates/django_filters/rest_framework/form.html:2
msgid "Field filters"
msgstr "Filtre poľa"

#: utils.py:308
msgid "exclude"
msgstr "neobsahuje"

#: widgets.py:58
msgid "All"
msgstr "Všetky"

#: widgets.py:162
msgid "Unknown"
msgstr "Neznáme"

#: widgets.py:162
msgid "Yes"
msgstr "Áno"

#: widgets.py:162
msgid "No"
msgstr "Nie"

#~ msgid "Any date"
#~ msgstr "Akýkoľvek dátum"
