from django.db import models
from django.conf import settings
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator
from django.utils.text import slugify
from stores.models import Store


class ProductCategory(models.Model):
    """
    تصنيفات المنتجات
    """
    name = models.CharField(
        max_length=100,
        unique=True,
        verbose_name=_('اسم التصنيف')
    )
    slug = models.SlugField(
        max_length=100,
        unique=True,
        blank=True,
        verbose_name=_('الرابط المختصر')
    )
    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('الوصف')
    )
    parent = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='children',
        verbose_name=_('التصنيف الأب')
    )
    image = models.ImageField(
        upload_to='category_images/',
        blank=True,
        null=True,
        verbose_name=_('صورة التصنيف')
    )
    is_active = models.BooleanField(
        default=True,
        verbose_name=_('نشط')
    )
    sort_order = models.PositiveIntegerField(
        default=0,
        verbose_name=_('ترتيب العرض')
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    class Meta:
        verbose_name = _('تصنيف المنتج')
        verbose_name_plural = _('تصنيفات المنتجات')
        ordering = ['sort_order', 'name']

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)


class Product(models.Model):
    """
    نموذج المنتج
    """
    STATUS_CHOICES = [
        ('draft', _('مسودة')),
        ('active', _('نشط')),
        ('inactive', _('غير نشط')),
        ('out_of_stock', _('نفد من المخزون')),
    ]

    store = models.ForeignKey(
        Store,
        on_delete=models.CASCADE,
        related_name='products',
        verbose_name=_('المتجر')
    )
    name = models.CharField(
        max_length=200,
        verbose_name=_('اسم المنتج')
    )
    slug = models.SlugField(
        max_length=200,
        blank=True,
        verbose_name=_('الرابط المختصر')
    )
    description = models.TextField(
        verbose_name=_('وصف المنتج')
    )
    short_description = models.CharField(
        max_length=500,
        blank=True,
        null=True,
        verbose_name=_('وصف مختصر')
    )
    category = models.ForeignKey(
        ProductCategory,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='products',
        verbose_name=_('التصنيف')
    )
    price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(0)],
        verbose_name=_('السعر')
    )
    compare_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        validators=[MinValueValidator(0)],
        verbose_name=_('سعر المقارنة')
    )
    cost_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        validators=[MinValueValidator(0)],
        verbose_name=_('سعر التكلفة')
    )
    sku = models.CharField(
        max_length=100,
        unique=True,
        verbose_name=_('رمز المنتج')
    )
    barcode = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('الباركود')
    )
    stock_quantity = models.PositiveIntegerField(
        default=0,
        verbose_name=_('كمية المخزون')
    )
    low_stock_threshold = models.PositiveIntegerField(
        default=5,
        verbose_name=_('حد التنبيه للمخزون المنخفض')
    )
    weight = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        null=True,
        blank=True,
        validators=[MinValueValidator(0)],
        verbose_name=_('الوزن (كجم)')
    )
    dimensions = models.JSONField(
        default=dict,
        blank=True,
        verbose_name=_('الأبعاد')
    )
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='draft',
        verbose_name=_('حالة المنتج')
    )
    is_featured = models.BooleanField(
        default=False,
        verbose_name=_('منتج مميز')
    )
    is_digital = models.BooleanField(
        default=False,
        verbose_name=_('منتج رقمي')
    )
    requires_shipping = models.BooleanField(
        default=True,
        verbose_name=_('يتطلب شحن')
    )
    meta_title = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        verbose_name=_('عنوان SEO')
    )
    meta_description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('وصف SEO')
    )
    tags = models.JSONField(
        default=list,
        blank=True,
        verbose_name=_('العلامات')
    )
    rating = models.DecimalField(
        max_digits=3,
        decimal_places=2,
        default=0.00,
        verbose_name=_('التقييم')
    )
    total_sales = models.PositiveIntegerField(
        default=0,
        verbose_name=_('إجمالي المبيعات')
    )
    views_count = models.PositiveIntegerField(
        default=0,
        verbose_name=_('عدد المشاهدات')
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )

    class Meta:
        verbose_name = _('منتج')
        verbose_name_plural = _('المنتجات')
        ordering = ['-created_at']
        unique_together = ['store', 'slug']

    def __str__(self):
        return f"{self.name} - {self.store.name}"

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)

    @property
    def is_active(self):
        return self.status == 'active'

    @property
    def is_in_stock(self):
        return self.stock_quantity > 0

    @property
    def is_low_stock(self):
        return self.stock_quantity <= self.low_stock_threshold

    @property
    def discount_percentage(self):
        if self.compare_price and self.compare_price > self.price:
            return round(((self.compare_price - self.price) / self.compare_price) * 100, 2)
        return 0


class ProductImage(models.Model):
    """
    صور المنتج
    """
    product = models.ForeignKey(
        Product,
        on_delete=models.CASCADE,
        related_name='images',
        verbose_name=_('المنتج')
    )
    image = models.ImageField(
        upload_to='product_images/',
        verbose_name=_('الصورة')
    )
    alt_text = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        verbose_name=_('النص البديل')
    )
    is_primary = models.BooleanField(
        default=False,
        verbose_name=_('صورة أساسية')
    )
    sort_order = models.PositiveIntegerField(
        default=0,
        verbose_name=_('ترتيب العرض')
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    class Meta:
        verbose_name = _('صورة المنتج')
        verbose_name_plural = _('صور المنتجات')
        ordering = ['sort_order', 'created_at']

    def __str__(self):
        return f"صورة {self.product.name}"


class ProductVariant(models.Model):
    """
    متغيرات المنتج (مثل الألوان والأحجام)
    """
    product = models.ForeignKey(
        Product,
        on_delete=models.CASCADE,
        related_name='variants',
        verbose_name=_('المنتج')
    )
    name = models.CharField(
        max_length=100,
        verbose_name=_('اسم المتغير')
    )
    value = models.CharField(
        max_length=100,
        verbose_name=_('قيمة المتغير')
    )
    price_adjustment = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0.00,
        verbose_name=_('تعديل السعر')
    )
    stock_quantity = models.PositiveIntegerField(
        default=0,
        verbose_name=_('كمية المخزون')
    )
    sku = models.CharField(
        max_length=100,
        unique=True,
        verbose_name=_('رمز المتغير')
    )
    is_active = models.BooleanField(
        default=True,
        verbose_name=_('نشط')
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    class Meta:
        verbose_name = _('متغير المنتج')
        verbose_name_plural = _('متغيرات المنتجات')
        unique_together = ['product', 'name', 'value']

    def __str__(self):
        return f"{self.product.name} - {self.name}: {self.value}"

    @property
    def final_price(self):
        return self.product.price + self.price_adjustment


class ProductReview(models.Model):
    """
    تقييمات المنتجات
    """
    RATING_CHOICES = [
        (1, _('نجمة واحدة')),
        (2, _('نجمتان')),
        (3, _('ثلاث نجوم')),
        (4, _('أربع نجوم')),
        (5, _('خمس نجوم')),
    ]

    product = models.ForeignKey(
        Product,
        on_delete=models.CASCADE,
        related_name='reviews',
        verbose_name=_('المنتج')
    )
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='product_reviews',
        verbose_name=_('المستخدم')
    )
    rating = models.PositiveIntegerField(
        choices=RATING_CHOICES,
        verbose_name=_('التقييم')
    )
    title = models.CharField(
        max_length=200,
        verbose_name=_('عنوان التقييم')
    )
    comment = models.TextField(
        verbose_name=_('التعليق')
    )
    is_verified_purchase = models.BooleanField(
        default=False,
        verbose_name=_('شراء مُتحقق منه')
    )
    is_approved = models.BooleanField(
        default=True,
        verbose_name=_('مُوافق عليه')
    )
    helpful_count = models.PositiveIntegerField(
        default=0,
        verbose_name=_('عدد الإعجابات')
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )

    class Meta:
        verbose_name = _('تقييم المنتج')
        verbose_name_plural = _('تقييمات المنتجات')
        ordering = ['-created_at']
        unique_together = ['product', 'user']

    def __str__(self):
        return f"تقييم {self.product.name} بواسطة {self.user.username}"
