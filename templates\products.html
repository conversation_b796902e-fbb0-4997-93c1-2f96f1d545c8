<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المنتجات - السوق الإلكتروني</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

        body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        .navbar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.8rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .product-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: none;
            border-radius: 20px;
            overflow: hidden;
            transition: all 0.4s ease;
            height: 100%;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            position: relative;
        }

        .product-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .product-card:hover::before {
            transform: scaleX(1);
        }

        .product-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
            background: rgba(255, 255, 255, 1);
        }

        .product-image {
            height: 250px;
            object-fit: cover;
            width: 100%;
            transition: transform 0.3s ease;
        }

        .product-card:hover .product-image {
            transform: scale(1.05);
        }
        .price {
            font-size: 1.2rem;
            font-weight: bold;
            color: #28a745;
        }
        .old-price {
            text-decoration: line-through;
            color: #6c757d;
            font-size: 0.9rem;
        }
        .discount-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #dc3545;
            color: white;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.8rem;
        }
        .rating {
            color: #ffc107;
        }
        .filter-sidebar {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        .search-box {
            border-radius: 25px;
            border: 2px solid #e9ecef;
            padding: 10px 20px;
        }
        .search-box:focus {
            border-color: #007bff;
            box-shadow: none;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-store"></i> السوق الإلكتروني
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/products/">المنتجات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/stores/">المتاجر</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/">
                            <i class="fas fa-cog"></i> لوحة التحكم
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-lg-3 mb-4">
                <div class="filter-sidebar">
                    <h5 class="mb-3">
                        <i class="fas fa-filter"></i> البحث والفلترة
                    </h5>
                    
                    <!-- Search -->
                    <div class="mb-3">
                        <input type="text" class="form-control search-box" id="searchInput" placeholder="ابحث عن المنتجات...">
                    </div>
                    
                    <!-- Categories -->
                    <div class="mb-3">
                        <h6>التصنيفات</h6>
                        <div id="categoriesFilter">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" value="electronics" id="electronics">
                                <label class="form-check-label" for="electronics">إلكترونيات</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" value="clothing" id="clothing">
                                <label class="form-check-label" for="clothing">ملابس</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" value="books" id="books">
                                <label class="form-check-label" for="books">كتب</label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Price Range -->
                    <div class="mb-3">
                        <h6>نطاق السعر</h6>
                        <div class="row">
                            <div class="col-6">
                                <input type="number" class="form-control" id="minPrice" placeholder="من">
                            </div>
                            <div class="col-6">
                                <input type="number" class="form-control" id="maxPrice" placeholder="إلى">
                            </div>
                        </div>
                    </div>
                    
                    <!-- Rating -->
                    <div class="mb-3">
                        <h6>التقييم</h6>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" value="5" id="rating5">
                            <label class="form-check-label" for="rating5">
                                <span class="rating">★★★★★</span>
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" value="4" id="rating4">
                            <label class="form-check-label" for="rating4">
                                <span class="rating">★★★★</span> فأكثر
                            </label>
                        </div>
                    </div>
                    
                    <button class="btn btn-primary w-100" onclick="applyFilters()">
                        <i class="fas fa-search"></i> تطبيق الفلاتر
                    </button>
                </div>
            </div>
            
            <!-- Products -->
            <div class="col-lg-9">
                <!-- Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>المنتجات</h2>
                    <div class="d-flex align-items-center">
                        <label for="sortBy" class="me-2">ترتيب حسب:</label>
                        <select class="form-select" id="sortBy" style="width: auto;">
                            <option value="newest">الأحدث</option>
                            <option value="price_low">السعر: من الأقل للأعلى</option>
                            <option value="price_high">السعر: من الأعلى للأقل</option>
                            <option value="rating">التقييم</option>
                            <option value="popular">الأكثر مبيعاً</option>
                        </select>
                    </div>
                </div>
                
                <!-- Products Grid -->
                <div class="row" id="productsGrid">
                    <!-- Products will be loaded here via JavaScript -->
                    <div class="col-12 text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                        <p class="mt-3">جاري تحميل المنتجات...</p>
                    </div>
                </div>
                
                <!-- Pagination -->
                <nav aria-label="صفحات المنتجات" class="mt-4">
                    <ul class="pagination justify-content-center" id="pagination">
                        <!-- Pagination will be generated by JavaScript -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container text-center">
            <p class="mb-0">&copy; 2024 السوق الإلكتروني. جميع الحقوق محفوظة.</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Load products from API
        async function loadProducts(page = 1, filters = {}) {
            try {
                const params = new URLSearchParams({
                    page: page,
                    ...filters
                });
                
                const response = await fetch(`/api/products/?${params}`);
                const data = await response.json();
                
                displayProducts(data.results || []);
                displayPagination(data);
            } catch (error) {
                console.error('Error loading products:', error);
                document.getElementById('productsGrid').innerHTML = `
                    <div class="col-12 text-center py-5">
                        <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                        <h5>خطأ في تحميل المنتجات</h5>
                        <p>يرجى المحاولة مرة أخرى لاحقاً</p>
                        <button class="btn btn-primary" onclick="loadProducts()">إعادة المحاولة</button>
                    </div>
                `;
            }
        }
        
        function displayProducts(products) {
            const grid = document.getElementById('productsGrid');
            
            if (products.length === 0) {
                grid.innerHTML = `
                    <div class="col-12 text-center py-5">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h5>لا توجد منتجات</h5>
                        <p>لم يتم العثور على منتجات تطابق معايير البحث</p>
                    </div>
                `;
                return;
            }
            
            grid.innerHTML = products.map(product => `
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card product-card h-100 position-relative">
                        ${product.discount_percentage > 0 ? `<div class="discount-badge">-${product.discount_percentage}%</div>` : ''}
                        <img src="${product.primary_image?.image || '/static/images/no-image.png'}" 
                             class="product-image" alt="${product.name}">
                        <div class="card-body d-flex flex-column">
                            <h6 class="card-title">${product.name}</h6>
                            <p class="card-text text-muted small flex-grow-1">${product.short_description || ''}</p>
                            <div class="mb-2">
                                <span class="badge bg-secondary">${product.store.name}</span>
                            </div>
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <span class="price">${product.price} ر.س</span>
                                    ${product.compare_price ? `<span class="old-price ms-2">${product.compare_price} ر.س</span>` : ''}
                                </div>
                                <div class="rating">
                                    ${'★'.repeat(Math.floor(product.rating))}${'☆'.repeat(5 - Math.floor(product.rating))}
                                </div>
                            </div>
                            <button class="btn btn-primary mt-2" onclick="addToCart('${product.id}')">
                                <i class="fas fa-cart-plus"></i> أضف للسلة
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');
        }
        
        function displayPagination(data) {
            // Simplified pagination - you can enhance this
            const pagination = document.getElementById('pagination');
            pagination.innerHTML = `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="loadProducts(1)">الصفحة الأولى</a>
                </li>
            `;
        }
        
        function applyFilters() {
            const filters = {
                search: document.getElementById('searchInput').value,
                min_price: document.getElementById('minPrice').value,
                max_price: document.getElementById('maxPrice').value,
                ordering: document.getElementById('sortBy').value
            };
            
            loadProducts(1, filters);
        }
        
        function addToCart(productId) {
            alert(`تم إضافة المنتج ${productId} إلى السلة (يتطلب تسجيل الدخول)`);
        }
        
        // Load products on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadProducts();
        });
        
        // Apply filters on sort change
        document.getElementById('sortBy').addEventListener('change', applyFilters);
    </script>
</body>
</html>
