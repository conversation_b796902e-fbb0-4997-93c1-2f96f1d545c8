# Generated by Django 5.2.4 on 2025-07-16 23:02

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('stores', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ProductCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='اسم التصنيف')),
                ('slug', models.SlugField(blank=True, max_length=100, unique=True, verbose_name='الرابط المختصر')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('image', models.ImageField(blank=True, null=True, upload_to='category_images/', verbose_name='صورة التصنيف')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('sort_order', models.PositiveIntegerField(default=0, verbose_name='ترتيب العرض')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='products.productcategory', verbose_name='التصنيف الأب')),
            ],
            options={
                'verbose_name': 'تصنيف المنتج',
                'verbose_name_plural': 'تصنيفات المنتجات',
                'ordering': ['sort_order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='Product',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم المنتج')),
                ('slug', models.SlugField(blank=True, max_length=200, verbose_name='الرابط المختصر')),
                ('description', models.TextField(verbose_name='وصف المنتج')),
                ('short_description', models.CharField(blank=True, max_length=500, null=True, verbose_name='وصف مختصر')),
                ('price', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='السعر')),
                ('compare_price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, validators=[django.core.validators.MinValueValidator(0)], verbose_name='سعر المقارنة')),
                ('cost_price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, validators=[django.core.validators.MinValueValidator(0)], verbose_name='سعر التكلفة')),
                ('sku', models.CharField(max_length=100, unique=True, verbose_name='رمز المنتج')),
                ('barcode', models.CharField(blank=True, max_length=100, null=True, verbose_name='الباركود')),
                ('stock_quantity', models.PositiveIntegerField(default=0, verbose_name='كمية المخزون')),
                ('low_stock_threshold', models.PositiveIntegerField(default=5, verbose_name='حد التنبيه للمخزون المنخفض')),
                ('weight', models.DecimalField(blank=True, decimal_places=2, max_digits=8, null=True, validators=[django.core.validators.MinValueValidator(0)], verbose_name='الوزن (كجم)')),
                ('dimensions', models.JSONField(blank=True, default=dict, verbose_name='الأبعاد')),
                ('status', models.CharField(choices=[('draft', 'مسودة'), ('active', 'نشط'), ('inactive', 'غير نشط'), ('out_of_stock', 'نفد من المخزون')], default='draft', max_length=20, verbose_name='حالة المنتج')),
                ('is_featured', models.BooleanField(default=False, verbose_name='منتج مميز')),
                ('is_digital', models.BooleanField(default=False, verbose_name='منتج رقمي')),
                ('requires_shipping', models.BooleanField(default=True, verbose_name='يتطلب شحن')),
                ('meta_title', models.CharField(blank=True, max_length=200, null=True, verbose_name='عنوان SEO')),
                ('meta_description', models.TextField(blank=True, null=True, verbose_name='وصف SEO')),
                ('tags', models.JSONField(blank=True, default=list, verbose_name='العلامات')),
                ('rating', models.DecimalField(decimal_places=2, default=0.0, max_digits=3, verbose_name='التقييم')),
                ('total_sales', models.PositiveIntegerField(default=0, verbose_name='إجمالي المبيعات')),
                ('views_count', models.PositiveIntegerField(default=0, verbose_name='عدد المشاهدات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('store', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='products', to='stores.store', verbose_name='المتجر')),
                ('category', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='products', to='products.productcategory', verbose_name='التصنيف')),
            ],
            options={
                'verbose_name': 'منتج',
                'verbose_name_plural': 'المنتجات',
                'ordering': ['-created_at'],
                'unique_together': {('store', 'slug')},
            },
        ),
        migrations.CreateModel(
            name='ProductImage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(upload_to='product_images/', verbose_name='الصورة')),
                ('alt_text', models.CharField(blank=True, max_length=200, null=True, verbose_name='النص البديل')),
                ('is_primary', models.BooleanField(default=False, verbose_name='صورة أساسية')),
                ('sort_order', models.PositiveIntegerField(default=0, verbose_name='ترتيب العرض')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='images', to='products.product', verbose_name='المنتج')),
            ],
            options={
                'verbose_name': 'صورة المنتج',
                'verbose_name_plural': 'صور المنتجات',
                'ordering': ['sort_order', 'created_at'],
            },
        ),
        migrations.CreateModel(
            name='ProductReview',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('rating', models.PositiveIntegerField(choices=[(1, 'نجمة واحدة'), (2, 'نجمتان'), (3, 'ثلاث نجوم'), (4, 'أربع نجوم'), (5, 'خمس نجوم')], verbose_name='التقييم')),
                ('title', models.CharField(max_length=200, verbose_name='عنوان التقييم')),
                ('comment', models.TextField(verbose_name='التعليق')),
                ('is_verified_purchase', models.BooleanField(default=False, verbose_name='شراء مُتحقق منه')),
                ('is_approved', models.BooleanField(default=True, verbose_name='مُوافق عليه')),
                ('helpful_count', models.PositiveIntegerField(default=0, verbose_name='عدد الإعجابات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reviews', to='products.product', verbose_name='المنتج')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='product_reviews', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'تقييم المنتج',
                'verbose_name_plural': 'تقييمات المنتجات',
                'ordering': ['-created_at'],
                'unique_together': {('product', 'user')},
            },
        ),
        migrations.CreateModel(
            name='ProductVariant',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم المتغير')),
                ('value', models.CharField(max_length=100, verbose_name='قيمة المتغير')),
                ('price_adjustment', models.DecimalField(decimal_places=2, default=0.0, max_digits=10, verbose_name='تعديل السعر')),
                ('stock_quantity', models.PositiveIntegerField(default=0, verbose_name='كمية المخزون')),
                ('sku', models.CharField(max_length=100, unique=True, verbose_name='رمز المتغير')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='variants', to='products.product', verbose_name='المنتج')),
            ],
            options={
                'verbose_name': 'متغير المنتج',
                'verbose_name_plural': 'متغيرات المنتجات',
                'unique_together': {('product', 'name', 'value')},
            },
        ),
    ]
