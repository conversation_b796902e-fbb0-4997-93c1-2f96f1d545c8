from django.shortcuts import render, get_object_or_404
from rest_framework import generics, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.db.models import Q
from django.utils import timezone
from .models import Cart, CartItem, Order, OrderItem
from .serializers import (
    CartSerializer, CartItemSerializer, OrderListSerializer,
    OrderDetailSerializer, OrderCreateSerializer
)
from products.models import Product, ProductVariant


class CartView(generics.RetrieveAPIView):
    """
    عرض سلة المشتريات للمستخدم الحالي
    """
    serializer_class = CartSerializer
    permission_classes = [IsAuthenticated]

    def get_object(self):
        cart, created = Cart.objects.get_or_create(user=self.request.user)
        return cart


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def add_to_cart(request):
    """
    إضافة منتج إلى سلة المشتريات
    """
    product_id = request.data.get('product_id')
    variant_id = request.data.get('variant_id')
    quantity = request.data.get('quantity', 1)

    try:
        quantity = int(quantity)
        if quantity <= 0:
            return Response({'error': 'الكمية يجب أن تكون أكبر من صفر'},
                          status=status.HTTP_400_BAD_REQUEST)
    except (ValueError, TypeError):
        return Response({'error': 'كمية غير صحيحة'},
                      status=status.HTTP_400_BAD_REQUEST)

    try:
        product = Product.objects.get(id=product_id, status='active')
    except Product.DoesNotExist:
        return Response({'error': 'المنتج غير موجود'},
                      status=status.HTTP_404_NOT_FOUND)

    variant = None
    if variant_id:
        try:
            variant = ProductVariant.objects.get(
                id=variant_id,
                product=product,
                is_active=True
            )
        except ProductVariant.DoesNotExist:
            return Response({'error': 'متغير المنتج غير موجود'},
                          status=status.HTTP_404_NOT_FOUND)

    # Check stock
    available_stock = variant.stock_quantity if variant else product.stock_quantity
    if quantity > available_stock:
        return Response({'error': f'الكمية المطلوبة غير متوفرة. المتوفر: {available_stock}'},
                      status=status.HTTP_400_BAD_REQUEST)

    # Get or create cart
    cart, created = Cart.objects.get_or_create(user=request.user)

    # Check if item already exists in cart
    cart_item, item_created = CartItem.objects.get_or_create(
        cart=cart,
        product=product,
        variant=variant,
        defaults={'quantity': quantity}
    )

    if not item_created:
        # Update quantity
        new_quantity = cart_item.quantity + quantity
        if new_quantity > available_stock:
            return Response({'error': f'الكمية الإجمالية تتجاوز المتوفر. المتوفر: {available_stock}'},
                          status=status.HTTP_400_BAD_REQUEST)
        cart_item.quantity = new_quantity
        cart_item.save()

    serializer = CartSerializer(cart)
    return Response(serializer.data, status=status.HTTP_200_OK)


@api_view(['PUT'])
@permission_classes([IsAuthenticated])
def update_cart_item(request, item_id):
    """
    تحديث كمية منتج في سلة المشتريات
    """
    try:
        cart_item = CartItem.objects.get(
            id=item_id,
            cart__user=request.user
        )
    except CartItem.DoesNotExist:
        return Response({'error': 'العنصر غير موجود في سلة المشتريات'},
                      status=status.HTTP_404_NOT_FOUND)

    quantity = request.data.get('quantity')
    try:
        quantity = int(quantity)
        if quantity <= 0:
            return Response({'error': 'الكمية يجب أن تكون أكبر من صفر'},
                          status=status.HTTP_400_BAD_REQUEST)
    except (ValueError, TypeError):
        return Response({'error': 'كمية غير صحيحة'},
                      status=status.HTTP_400_BAD_REQUEST)

    # Check stock
    available_stock = (cart_item.variant.stock_quantity
                      if cart_item.variant
                      else cart_item.product.stock_quantity)

    if quantity > available_stock:
        return Response({'error': f'الكمية المطلوبة غير متوفرة. المتوفر: {available_stock}'},
                      status=status.HTTP_400_BAD_REQUEST)

    cart_item.quantity = quantity
    cart_item.save()

    serializer = CartSerializer(cart_item.cart)
    return Response(serializer.data, status=status.HTTP_200_OK)


@api_view(['DELETE'])
@permission_classes([IsAuthenticated])
def remove_from_cart(request, item_id):
    """
    حذف منتج من سلة المشتريات
    """
    try:
        cart_item = CartItem.objects.get(
            id=item_id,
            cart__user=request.user
        )
    except CartItem.DoesNotExist:
        return Response({'error': 'العنصر غير موجود في سلة المشتريات'},
                      status=status.HTTP_404_NOT_FOUND)

    cart = cart_item.cart
    cart_item.delete()

    serializer = CartSerializer(cart)
    return Response(serializer.data, status=status.HTTP_200_OK)


@api_view(['DELETE'])
@permission_classes([IsAuthenticated])
def clear_cart(request):
    """
    مسح جميع المنتجات من سلة المشتريات
    """
    try:
        cart = Cart.objects.get(user=request.user)
        cart.items.all().delete()
        serializer = CartSerializer(cart)
        return Response(serializer.data, status=status.HTTP_200_OK)
    except Cart.DoesNotExist:
        return Response({'message': 'سلة المشتريات فارغة'},
                      status=status.HTTP_200_OK)


class OrderListView(generics.ListAPIView):
    """
    عرض قائمة طلبات المستخدم
    """
    serializer_class = OrderListSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return Order.objects.filter(user=self.request.user).order_by('-created_at')


class OrderDetailView(generics.RetrieveAPIView):
    """
    عرض تفاصيل طلب واحد
    """
    serializer_class = OrderDetailSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return Order.objects.filter(user=self.request.user)


class OrderCreateView(generics.CreateAPIView):
    """
    إنشاء طلب جديد من سلة المشتريات
    """
    serializer_class = OrderCreateSerializer
    permission_classes = [IsAuthenticated]


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def update_order_status(request, order_id):
    """
    تحديث حالة الطلب (للمتاجر والمدراء)
    """
    try:
        order = Order.objects.get(id=order_id)
    except Order.DoesNotExist:
        return Response({'error': 'الطلب غير موجود'},
                      status=status.HTTP_404_NOT_FOUND)

    user = request.user
    new_status = request.data.get('status')
    notes = request.data.get('notes', '')
    tracking_number = request.data.get('tracking_number', '')

    # Check permissions
    if not (user.is_admin_user or user.is_staff):
        # Check if user owns a store that has items in this order
        user_stores = user.stores.filter(status='approved')
        order_stores = order.stores
        if not user_stores.filter(id__in=order_stores.values_list('id', flat=True)).exists():
            return Response({'error': 'غير مصرح لك بتحديث هذا الطلب'},
                          status=status.HTTP_403_FORBIDDEN)

    # Validate status transition
    valid_transitions = {
        'pending': ['paid', 'cancelled'],
        'paid': ['processing', 'cancelled'],
        'processing': ['shipped', 'cancelled'],
        'shipped': ['delivered'],
        'delivered': [],
        'cancelled': [],
        'refunded': []
    }

    if new_status not in valid_transitions.get(order.status, []):
        return Response({'error': f'لا يمكن تغيير الحالة من {order.get_status_display()} إلى {dict(Order.STATUS_CHOICES)[new_status]}'},
                      status=status.HTTP_400_BAD_REQUEST)

    # Update order
    order.status = new_status
    if tracking_number:
        order.tracking_number = tracking_number

    # Set timestamps
    if new_status == 'shipped':
        order.shipped_at = timezone.now()
    elif new_status == 'delivered':
        order.delivered_at = timezone.now()

    order.save()

    # Create status history
    OrderStatusHistory.objects.create(
        order=order,
        status=new_status,
        notes=notes,
        changed_by=user
    )

    # TODO: Send notifications to customer and vendors

    serializer = OrderDetailSerializer(order)
    return Response(serializer.data)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def cancel_order(request, order_id):
    """
    إلغاء الطلب
    """
    try:
        order = Order.objects.get(id=order_id, user=request.user)
    except Order.DoesNotExist:
        return Response({'error': 'الطلب غير موجود'},
                      status=status.HTTP_404_NOT_FOUND)

    if not order.can_be_cancelled:
        return Response({'error': 'لا يمكن إلغاء هذا الطلب'},
                      status=status.HTTP_400_BAD_REQUEST)

    order.status = 'cancelled'
    order.save()

    # Create status history
    OrderStatusHistory.objects.create(
        order=order,
        status='cancelled',
        notes='تم الإلغاء بواسطة العميل',
        changed_by=request.user
    )

    # TODO: Process refund if payment was completed

    serializer = OrderDetailSerializer(order)
    return Response(serializer.data)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def vendor_orders(request):
    """
    عرض طلبات المتجر (للمتاجر)
    """
    user = request.user
    if not user.is_vendor:
        return Response({'error': 'غير مصرح'}, status=status.HTTP_403_FORBIDDEN)

    stores = user.stores.filter(status='approved')
    if not stores.exists():
        return Response({'error': 'لا يوجد متجر مُوافق عليه'},
                      status=status.HTTP_404_NOT_FOUND)

    # Get orders that contain items from user's stores
    orders = Order.objects.filter(
        items__product__store__in=stores
    ).distinct().order_by('-created_at')

    # Apply filters
    status_filter = request.GET.get('status')
    if status_filter:
        orders = orders.filter(status=status_filter)

    # Paginate
    from django.core.paginator import Paginator
    paginator = Paginator(orders, 20)
    page_number = request.GET.get('page', 1)
    page_obj = paginator.get_page(page_number)

    serializer = OrderListSerializer(page_obj.object_list, many=True)

    return Response({
        'results': serializer.data,
        'count': paginator.count,
        'num_pages': paginator.num_pages,
        'current_page': page_obj.number,
        'has_next': page_obj.has_next(),
        'has_previous': page_obj.has_previous()
    })


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def order_statistics(request):
    """
    إحصائيات الطلبات
    """
    user = request.user

    if user.is_admin_user or user.is_staff:
        # Admin statistics
        from django.db.models import Count, Sum

        total_orders = Order.objects.count()
        total_revenue = Order.objects.filter(
            status__in=['delivered', 'shipped']
        ).aggregate(total=Sum('total_amount'))['total'] or 0

        orders_by_status = Order.objects.values('status').annotate(
            count=Count('id')
        )

        return Response({
            'total_orders': total_orders,
            'total_revenue': total_revenue,
            'orders_by_status': list(orders_by_status)
        })

    elif user.is_vendor:
        # Vendor statistics
        stores = user.stores.filter(status='approved')
        if not stores.exists():
            return Response({'error': 'لا يوجد متجر مُوافق عليه'},
                          status=status.HTTP_404_NOT_FOUND)

        from django.db.models import Count, Sum

        orders = Order.objects.filter(items__product__store__in=stores).distinct()

        total_orders = orders.count()
        total_revenue = orders.filter(
            status__in=['delivered', 'shipped']
        ).aggregate(total=Sum('items__total_price'))['total'] or 0

        orders_by_status = orders.values('status').annotate(
            count=Count('id')
        )

        return Response({
            'total_orders': total_orders,
            'total_revenue': total_revenue,
            'orders_by_status': list(orders_by_status)
        })

    else:
        # Customer statistics
        orders = Order.objects.filter(user=user)

        total_orders = orders.count()
        total_spent = orders.filter(
            status__in=['delivered', 'shipped']
        ).aggregate(total=Sum('total_amount'))['total'] or 0

        orders_by_status = orders.values('status').annotate(
            count=Count('id')
        )

        return Response({
            'total_orders': total_orders,
            'total_spent': total_spent,
            'orders_by_status': list(orders_by_status)
        })
