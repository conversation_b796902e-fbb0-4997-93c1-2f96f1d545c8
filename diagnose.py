#!/usr/bin/env python
"""
سكريبت تشخيص المشاكل
Diagnostic script for troubleshooting
"""
import sys
import os
import subprocess

def check_python_version():
    """فحص إصدار Python"""
    print(f"🐍 إصدار Python: {sys.version}")
    if sys.version_info < (3, 8):
        print("⚠️  تحذير: يُنصح باستخدام Python 3.8 أو أحدث")
    else:
        print("✅ إصدار Python مناسب")

def check_virtual_env():
    """فحص البيئة الافتراضية"""
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("✅ البيئة الافتراضية مفعلة")
        print(f"📁 مسار البيئة: {sys.prefix}")
    else:
        print("⚠️  البيئة الافتراضية غير مفعلة")
        print("💡 قم بتشغيل: source marketplace_env/Scripts/activate")

def check_required_packages():
    """فحص المكتبات المطلوبة"""
    required_packages = [
        'django',
        'djangorestframework', 
        'python-decouple',
        'pillow',
        'psycopg2-binary',
        'celery',
        'redis',
        'stripe'
    ]
    
    print("\n📦 فحص المكتبات المطلوبة:")
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - مفقود")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n💡 لتثبيت المكتبات المفقودة:")
        print(f"pip install {' '.join(missing_packages)}")
    else:
        print("\n🎉 جميع المكتبات مثبتة!")

def check_django_setup():
    """فحص إعداد Django"""
    try:
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'marketplace.settings')
        import django
        django.setup()
        print("✅ Django يعمل بشكل صحيح")
        
        # فحص قاعدة البيانات
        from django.db import connection
        cursor = connection.cursor()
        print("✅ الاتصال بقاعدة البيانات يعمل")
        
    except Exception as e:
        print(f"❌ خطأ في Django: {e}")

def check_files():
    """فحص الملفات المطلوبة"""
    required_files = [
        'manage.py',
        'marketplace/settings.py',
        'marketplace/urls.py',
        'requirements.txt',
        '.env'
    ]
    
    print("\n📄 فحص الملفات المطلوبة:")
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - مفقود")

def suggest_fixes():
    """اقتراح حلول"""
    print("\n💡 حلول مقترحة:")
    print("1. تأكد من تفعيل البيئة الافتراضية:")
    print("   source marketplace_env/Scripts/activate")
    print("\n2. تثبيت المكتبات المطلوبة:")
    print("   pip install -r requirements.txt")
    print("\n3. إذا فشل التثبيت، جرب:")
    print("   pip install --upgrade pip")
    print("   pip install python-decouple")
    print("\n4. للتحقق من Django:")
    print("   python manage.py check")
    print("\n5. لتطبيق الهجرات:")
    print("   python manage.py migrate")

def main():
    print("🔍 تشخيص مشاكل المشروع")
    print("=" * 40)
    
    check_python_version()
    print()
    check_virtual_env()
    print()
    check_required_packages()
    print()
    check_files()
    print()
    
    try:
        check_django_setup()
    except:
        pass
    
    suggest_fixes()

if __name__ == "__main__":
    main()
