from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils.translation import gettext_lazy as _


class User(AbstractUser):
    """
    نموذج المستخدم المخصص
    """
    USER_TYPE_CHOICES = [
        ('customer', _('عميل')),
        ('vendor', _('صاحب متجر')),
        ('admin', _('مدير')),
    ]

    user_type = models.CharField(
        max_length=20,
        choices=USER_TYPE_CHOICES,
        default='customer',
        verbose_name=_('نوع المستخدم')
    )
    phone = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        verbose_name=_('رقم الهاتف')
    )
    address = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('العنوان')
    )
    date_of_birth = models.DateField(
        blank=True,
        null=True,
        verbose_name=_('تاريخ الميلاد')
    )
    profile_picture = models.ImageField(
        upload_to='profile_pictures/',
        blank=True,
        null=True,
        verbose_name=_('صورة الملف الشخصي')
    )
    is_verified = models.BooleanField(
        default=False,
        verbose_name=_('مُتحقق منه')
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )

    class Meta:
        verbose_name = _('مستخدم')
        verbose_name_plural = _('المستخدمون')

    def __str__(self):
        return f"{self.username} ({self.get_user_type_display()})"

    @property
    def is_customer(self):
        return self.user_type == 'customer'

    @property
    def is_vendor(self):
        return self.user_type == 'vendor'

    @property
    def is_admin_user(self):
        return self.user_type == 'admin'


class UserProfile(models.Model):
    """
    ملف تعريف إضافي للمستخدم
    """
    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name='profile',
        verbose_name=_('المستخدم')
    )
    bio = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('نبذة شخصية')
    )
    website = models.URLField(
        blank=True,
        null=True,
        verbose_name=_('الموقع الإلكتروني')
    )
    social_media_links = models.JSONField(
        default=dict,
        blank=True,
        verbose_name=_('روابط وسائل التواصل الاجتماعي')
    )
    preferences = models.JSONField(
        default=dict,
        blank=True,
        verbose_name=_('التفضيلات')
    )

    class Meta:
        verbose_name = _('ملف تعريف المستخدم')
        verbose_name_plural = _('ملفات تعريف المستخدمين')

    def __str__(self):
        return f"ملف تعريف {self.user.username}"
