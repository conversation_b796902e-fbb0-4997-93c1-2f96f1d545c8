from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from .models import Payment, Commission, Payout


@admin.register(Payment)
class PaymentAdmin(admin.ModelAdmin):
    list_display = ('order', 'user', 'payment_method', 'status', 'amount', 'currency', 'created_at')
    list_filter = ('payment_method', 'status', 'currency', 'created_at')
    search_fields = ('order__order_number', 'user__username', 'user__email', 'gateway_transaction_id')
    ordering = ('-created_at',)
    readonly_fields = ('id', 'created_at', 'updated_at', 'completed_at')

    fieldsets = (
        (_('معلومات الدفع'), {
            'fields': ('id', 'order', 'user', 'payment_method', 'status', 'amount', 'currency')
        }),
        (_('معلومات البوابة'), {
            'fields': ('gateway_transaction_id', 'gateway_response'),
            'classes': ('collapse',)
        }),
        (_('معلومات إضافية'), {
            'fields': ('notes', 'failure_reason')
        }),
        (_('التواريخ'), {
            'fields': ('created_at', 'updated_at', 'completed_at'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('order', 'user')


@admin.register(Commission)
class CommissionAdmin(admin.ModelAdmin):
    list_display = ('order', 'store', 'order_amount', 'commission_rate', 'commission_amount', 'store_amount', 'status', 'created_at')
    list_filter = ('status', 'created_at', 'paid_at')
    search_fields = ('order__order_number', 'store__name')
    ordering = ('-created_at',)
    readonly_fields = ('id', 'created_at', 'paid_at')

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('order', 'store', 'payment')


@admin.register(Payout)
class PayoutAdmin(admin.ModelAdmin):
    list_display = ('store', 'amount', 'currency', 'status', 'bank_name', 'account_number', 'created_at')
    list_filter = ('status', 'currency', 'created_at', 'processed_at')
    search_fields = ('store__name', 'account_holder_name', 'bank_name')
    ordering = ('-created_at',)
    readonly_fields = ('id', 'created_at', 'processed_at')

    fieldsets = (
        (_('معلومات الدفعة'), {
            'fields': ('id', 'store', 'amount', 'currency', 'status')
        }),
        (_('معلومات البنك'), {
            'fields': ('bank_name', 'account_number', 'account_holder_name')
        }),
        (_('معلومات إضافية'), {
            'fields': ('notes', 'failure_reason')
        }),
        (_('التواريخ'), {
            'fields': ('created_at', 'processed_at'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('store')
