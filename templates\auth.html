<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - السوق الإلكتروني</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

        body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        body::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
            animation: float 20s infinite linear;
        }

        @keyframes float {
            0% { transform: translateX(0) translateY(0); }
            100% { transform: translateX(-50px) translateY(-50px); }
        }
        .auth-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
            overflow: hidden;
            max-width: 1000px;
            margin: auto;
            position: relative;
            z-index: 2;
        }
        .auth-form {
            padding: 40px;
        }
        .auth-image {
            background: linear-gradient(45deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
            padding: 40px;
        }
        .form-floating label {
            right: 12px;
            left: auto;
        }
        .btn-social {
            border: 1px solid #ddd;
            padding: 12px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }
        .btn-social:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        .divider {
            text-align: center;
            margin: 20px 0;
            position: relative;
        }
        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #ddd;
        }
        .divider span {
            background: white;
            padding: 0 15px;
            color: #666;
        }
        .user-type-selector {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        .user-type-card {
            flex: 1;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .user-type-card:hover {
            border-color: #007bff;
        }
        .user-type-card.selected {
            border-color: #007bff;
            background-color: #f8f9ff;
        }
        .user-type-card i {
            font-size: 2rem;
            margin-bottom: 10px;
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="auth-container">
            <div class="row g-0">
                <div class="col-lg-6">
                    <div class="auth-image">
                        <div>
                            <i class="fas fa-store fa-5x mb-4"></i>
                            <h2>مرحباً بك في السوق الإلكتروني</h2>
                            <p class="lead">منصة شاملة تجمع بين المتاجر والعملاء</p>
                            <div class="mt-4">
                                <div class="d-flex justify-content-center gap-3">
                                    <div class="text-center">
                                        <i class="fas fa-shopping-bag fa-2x mb-2"></i>
                                        <div>تسوق آمن</div>
                                    </div>
                                    <div class="text-center">
                                        <i class="fas fa-truck fa-2x mb-2"></i>
                                        <div>شحن سريع</div>
                                    </div>
                                    <div class="text-center">
                                        <i class="fas fa-star fa-2x mb-2"></i>
                                        <div>جودة عالية</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-6">
                    <div class="auth-form">
                        <!-- Login Form -->
                        <div id="loginForm">
                            <h3 class="mb-4 text-center">تسجيل الدخول</h3>
                            
                            <form>
                                <div class="form-floating mb-3">
                                    <input type="email" class="form-control" id="loginEmail" required>
                                    <label for="loginEmail">البريد الإلكتروني</label>
                                </div>
                                
                                <div class="form-floating mb-3">
                                    <input type="password" class="form-control" id="loginPassword" required>
                                    <label for="loginPassword">كلمة المرور</label>
                                </div>
                                
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="rememberMe">
                                        <label class="form-check-label" for="rememberMe">
                                            تذكرني
                                        </label>
                                    </div>
                                    <a href="#" class="text-decoration-none" onclick="showForgotPassword()">
                                        نسيت كلمة المرور؟
                                    </a>
                                </div>
                                
                                <button type="submit" class="btn btn-primary w-100 btn-lg mb-3" onclick="login(event)">
                                    <i class="fas fa-sign-in-alt"></i> تسجيل الدخول
                                </button>
                            </form>
                            
                            <div class="divider">
                                <span>أو</span>
                            </div>
                            
                            <button class="btn btn-outline-danger w-100 btn-social">
                                <i class="fab fa-google"></i> تسجيل الدخول بـ Google
                            </button>
                            
                            <button class="btn btn-outline-primary w-100 btn-social">
                                <i class="fab fa-facebook"></i> تسجيل الدخول بـ Facebook
                            </button>
                            
                            <div class="text-center mt-4">
                                <span>ليس لديك حساب؟ </span>
                                <a href="#" class="text-decoration-none" onclick="showRegisterForm()">
                                    إنشاء حساب جديد
                                </a>
                            </div>
                        </div>
                        
                        <!-- Register Form -->
                        <div id="registerForm" style="display: none;">
                            <h3 class="mb-4 text-center">إنشاء حساب جديد</h3>
                            
                            <!-- User Type Selection -->
                            <div class="user-type-selector">
                                <div class="user-type-card" data-type="customer" onclick="selectUserType('customer')">
                                    <i class="fas fa-user text-primary"></i>
                                    <strong>عميل</strong>
                                    <div><small>للتسوق والشراء</small></div>
                                </div>
                                <div class="user-type-card" data-type="vendor" onclick="selectUserType('vendor')">
                                    <i class="fas fa-store text-success"></i>
                                    <strong>صاحب متجر</strong>
                                    <div><small>لبيع المنتجات</small></div>
                                </div>
                            </div>
                            
                            <form>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-floating mb-3">
                                            <input type="text" class="form-control" id="firstName" required>
                                            <label for="firstName">الاسم الأول</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-floating mb-3">
                                            <input type="text" class="form-control" id="lastName" required>
                                            <label for="lastName">اسم العائلة</label>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="form-floating mb-3">
                                    <input type="email" class="form-control" id="registerEmail" required>
                                    <label for="registerEmail">البريد الإلكتروني</label>
                                </div>
                                
                                <div class="form-floating mb-3">
                                    <input type="tel" class="form-control" id="phone" required>
                                    <label for="phone">رقم الهاتف</label>
                                </div>
                                
                                <div class="form-floating mb-3">
                                    <input type="password" class="form-control" id="registerPassword" required>
                                    <label for="registerPassword">كلمة المرور</label>
                                </div>
                                
                                <div class="form-floating mb-3">
                                    <input type="password" class="form-control" id="confirmPassword" required>
                                    <label for="confirmPassword">تأكيد كلمة المرور</label>
                                </div>
                                
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="agreeTerms" required>
                                    <label class="form-check-label" for="agreeTerms">
                                        أوافق على <a href="#" class="text-decoration-none">الشروط والأحكام</a>
                                        و <a href="#" class="text-decoration-none">سياسة الخصوصية</a>
                                    </label>
                                </div>
                                
                                <button type="submit" class="btn btn-success w-100 btn-lg mb-3" onclick="register(event)">
                                    <i class="fas fa-user-plus"></i> إنشاء الحساب
                                </button>
                            </form>
                            
                            <div class="text-center mt-4">
                                <span>لديك حساب بالفعل؟ </span>
                                <a href="#" class="text-decoration-none" onclick="showLoginForm()">
                                    تسجيل الدخول
                                </a>
                            </div>
                        </div>
                        
                        <!-- Forgot Password Form -->
                        <div id="forgotPasswordForm" style="display: none;">
                            <h3 class="mb-4 text-center">استعادة كلمة المرور</h3>
                            
                            <div class="text-center mb-4">
                                <i class="fas fa-key fa-3x text-muted mb-3"></i>
                                <p class="text-muted">أدخل بريدك الإلكتروني وسنرسل لك رابط لإعادة تعيين كلمة المرور</p>
                            </div>
                            
                            <form>
                                <div class="form-floating mb-3">
                                    <input type="email" class="form-control" id="forgotEmail" required>
                                    <label for="forgotEmail">البريد الإلكتروني</label>
                                </div>
                                
                                <button type="submit" class="btn btn-primary w-100 btn-lg mb-3" onclick="resetPassword(event)">
                                    <i class="fas fa-paper-plane"></i> إرسال رابط الاستعادة
                                </button>
                            </form>
                            
                            <div class="text-center mt-4">
                                <a href="#" class="text-decoration-none" onclick="showLoginForm()">
                                    <i class="fas fa-arrow-right"></i> العودة لتسجيل الدخول
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let selectedUserType = 'customer';

        function showLoginForm() {
            document.getElementById('loginForm').style.display = 'block';
            document.getElementById('registerForm').style.display = 'none';
            document.getElementById('forgotPasswordForm').style.display = 'none';
        }

        function showRegisterForm() {
            document.getElementById('loginForm').style.display = 'none';
            document.getElementById('registerForm').style.display = 'block';
            document.getElementById('forgotPasswordForm').style.display = 'none';
        }

        function showForgotPassword() {
            document.getElementById('loginForm').style.display = 'none';
            document.getElementById('registerForm').style.display = 'none';
            document.getElementById('forgotPasswordForm').style.display = 'block';
        }

        function selectUserType(type) {
            selectedUserType = type;
            
            // Remove selected class from all cards
            document.querySelectorAll('.user-type-card').forEach(card => {
                card.classList.remove('selected');
            });
            
            // Add selected class to clicked card
            document.querySelector(`[data-type="${type}"]`).classList.add('selected');
        }

        function login(event) {
            event.preventDefault();
            
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;
            
            if (!email || !password) {
                alert('يرجى ملء جميع الحقول');
                return;
            }
            
            // Simulate login API call
            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري تسجيل الدخول...';
            btn.disabled = true;
            
            setTimeout(() => {
                // Check demo credentials
                if (email === '<EMAIL>' && password === 'admin123') {
                    alert('مرحباً بك! تم تسجيل الدخول بنجاح');
                    window.location.href = '/admin/';
                } else if (email === '<EMAIL>' && password === 'customer123') {
                    alert('مرحباً بك! تم تسجيل الدخول بنجاح');
                    window.location.href = '/';
                } else if (email === '<EMAIL>' && password === 'vendor123') {
                    alert('مرحباً بك! تم تسجيل الدخول بنجاح');
                    window.location.href = '/vendor/dashboard/';
                } else {
                    alert('بيانات الدخول غير صحيحة');
                    btn.innerHTML = originalText;
                    btn.disabled = false;
                }
            }, 1500);
        }

        function register(event) {
            event.preventDefault();
            
            const firstName = document.getElementById('firstName').value;
            const lastName = document.getElementById('lastName').value;
            const email = document.getElementById('registerEmail').value;
            const phone = document.getElementById('phone').value;
            const password = document.getElementById('registerPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            const agreeTerms = document.getElementById('agreeTerms').checked;
            
            if (!firstName || !lastName || !email || !phone || !password || !confirmPassword) {
                alert('يرجى ملء جميع الحقول');
                return;
            }
            
            if (password !== confirmPassword) {
                alert('كلمة المرور وتأكيد كلمة المرور غير متطابقتين');
                return;
            }
            
            if (!agreeTerms) {
                alert('يجب الموافقة على الشروط والأحكام');
                return;
            }
            
            // Simulate registration API call
            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري إنشاء الحساب...';
            btn.disabled = true;
            
            setTimeout(() => {
                alert(`تم إنشاء حسابك بنجاح كـ ${selectedUserType === 'customer' ? 'عميل' : 'صاحب متجر'}!\nيرجى تفعيل حسابك من خلال الرابط المرسل على بريدك الإلكتروني.`);
                showLoginForm();
                btn.innerHTML = originalText;
                btn.disabled = false;
            }, 2000);
        }

        function resetPassword(event) {
            event.preventDefault();
            
            const email = document.getElementById('forgotEmail').value;
            
            if (!email) {
                alert('يرجى إدخال البريد الإلكتروني');
                return;
            }
            
            // Simulate password reset API call
            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإرسال...';
            btn.disabled = true;
            
            setTimeout(() => {
                alert('تم إرسال رابط استعادة كلمة المرور إلى بريدك الإلكتروني');
                showLoginForm();
                btn.innerHTML = originalText;
                btn.disabled = false;
            }, 1500);
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            selectUserType('customer');
        });
    </script>
</body>
</html>
