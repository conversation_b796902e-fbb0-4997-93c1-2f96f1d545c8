<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء متجر جديد - السوق الإلكتروني</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

        body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        .navbar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.8rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .form-section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 25px;
            box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
        }
        .form-floating label {
            right: 12px;
            left: auto;
        }
        .image-upload-container {
            position: relative;
            border: 2px dashed #ddd;
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #fafafa;
        }
        .image-upload-container:hover {
            border-color: #007bff;
            background-color: #f8f9ff;
        }
        .image-preview {
            max-width: 200px;
            max-height: 200px;
            border-radius: 10px;
            margin: 20px auto;
            display: block;
        }
        .logo-preview {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            object-fit: cover;
            border: 4px solid #fff;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        .banner-preview {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 10px;
        }
        .commission-info {
            background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border: 1px solid #e1bee7;
        }
        .feature-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            margin-bottom: 15px;
            border: 1px solid #e9ecef;
        }
        .feature-card i {
            font-size: 2.5rem;
            margin-bottom: 15px;
            color: #007bff;
        }
        .progress-indicator {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 25px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        .step-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        .step-item.completed {
            background: #d4edda;
            color: #155724;
        }
        .step-item.active {
            background: #cce5ff;
            color: #004085;
        }
        .step-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #6c757d;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-left: 15px;
        }
        .step-item.completed .step-number {
            background: #28a745;
        }
        .step-item.active .step-number {
            background: #007bff;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-store"></i> السوق الإلكتروني
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/profile/">
                    <i class="fas fa-user"></i> حسابي
                </a>
                <a class="nav-link" href="/logout/">
                    <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-lg-8">
                <div class="text-center mb-4">
                    <h1 class="display-5">
                        <i class="fas fa-store-alt text-primary"></i> إنشاء متجرك الإلكتروني
                    </h1>
                    <p class="lead text-muted">ابدأ رحلتك في البيع الإلكتروني معنا</p>
                </div>

                <form id="storeForm">
                    <!-- Store Information -->
                    <div class="form-section">
                        <h4 class="mb-4">
                            <i class="fas fa-info-circle text-primary"></i> معلومات المتجر
                        </h4>
                        
                        <div class="form-floating mb-3">
                            <input type="text" class="form-control" id="storeName" required>
                            <label for="storeName">اسم المتجر *</label>
                        </div>
                        
                        <div class="form-floating mb-3">
                            <textarea class="form-control" id="storeDescription" style="height: 120px" required></textarea>
                            <label for="storeDescription">وصف المتجر *</label>
                        </div>
                        
                        <div class="form-floating mb-3">
                            <select class="form-select" id="storeCategory" required>
                                <option value="">اختر تصنيف المتجر</option>
                                <option value="electronics">إلكترونيات وتقنية</option>
                                <option value="fashion">أزياء وموضة</option>
                                <option value="books">كتب ومكتبات</option>
                                <option value="home">منزل وحديقة</option>
                                <option value="sports">رياضة ولياقة</option>
                                <option value="beauty">جمال وعناية</option>
                                <option value="food">طعام ومشروبات</option>
                                <option value="automotive">سيارات وقطع غيار</option>
                                <option value="toys">ألعاب وأطفال</option>
                                <option value="health">صحة وطب</option>
                            </select>
                            <label for="storeCategory">تصنيف المتجر *</label>
                        </div>
                    </div>

                    <!-- Store Images -->
                    <div class="form-section">
                        <h4 class="mb-4">
                            <i class="fas fa-images text-primary"></i> صور المتجر
                        </h4>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <h6>شعار المتجر</h6>
                                <div class="image-upload-container" onclick="document.getElementById('logoInput').click()">
                                    <div id="logoPreview">
                                        <i class="fas fa-image fa-3x text-muted mb-3"></i>
                                        <h6>انقر لرفع الشعار</h6>
                                        <small class="text-muted">PNG, JPG (مربع الشكل مفضل)</small>
                                    </div>
                                    <input type="file" id="logoInput" accept="image/*" style="display: none;" onchange="previewLogo(event)">
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <h6>صورة الغلاف</h6>
                                <div class="image-upload-container" onclick="document.getElementById('bannerInput').click()">
                                    <div id="bannerPreview">
                                        <i class="fas fa-image fa-3x text-muted mb-3"></i>
                                        <h6>انقر لرفع صورة الغلاف</h6>
                                        <small class="text-muted">PNG, JPG (1200x400 مفضل)</small>
                                    </div>
                                    <input type="file" id="bannerInput" accept="image/*" style="display: none;" onchange="previewBanner(event)">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Contact Information -->
                    <div class="form-section">
                        <h4 class="mb-4">
                            <i class="fas fa-address-book text-primary"></i> معلومات التواصل
                        </h4>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="email" class="form-control" id="storeEmail" required>
                                    <label for="storeEmail">البريد الإلكتروني *</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="tel" class="form-control" id="storePhone" required>
                                    <label for="storePhone">رقم الهاتف *</label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-floating mb-3">
                            <textarea class="form-control" id="storeAddress" style="height: 100px" required></textarea>
                            <label for="storeAddress">العنوان الكامل *</label>
                        </div>
                        
                        <div class="form-floating mb-3">
                            <input type="url" class="form-control" id="storeWebsite">
                            <label for="storeWebsite">الموقع الإلكتروني (اختياري)</label>
                        </div>
                    </div>

                    <!-- Commission Information -->
                    <div class="commission-info">
                        <h5 class="mb-3">
                            <i class="fas fa-percentage text-primary"></i> معلومات العمولة
                        </h5>
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <p class="mb-2"><strong>عمولة المنصة: 5%</strong></p>
                                <p class="text-muted mb-0">
                                    يتم خصم 5% من قيمة كل عملية بيع كعمولة للمنصة. 
                                    هذا يشمل خدمات الدفع، الاستضافة، والدعم التقني.
                                </p>
                            </div>
                            <div class="col-md-4 text-center">
                                <div class="bg-white rounded p-3">
                                    <h3 class="text-primary mb-0">5%</h3>
                                    <small>عمولة المنصة</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Terms and Conditions -->
                    <div class="form-section">
                        <h4 class="mb-4">
                            <i class="fas fa-file-contract text-primary"></i> الشروط والأحكام
                        </h4>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="agreeTerms" required>
                            <label class="form-check-label" for="agreeTerms">
                                أوافق على <a href="#" class="text-decoration-none" data-bs-toggle="modal" data-bs-target="#termsModal">شروط وأحكام البائعين</a>
                            </label>
                        </div>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="agreeCommission" required>
                            <label class="form-check-label" for="agreeCommission">
                                أوافق على نظام العمولة (5% من قيمة المبيعات)
                            </label>
                        </div>
                        
                        <div class="form-check mb-4">
                            <input class="form-check-input" type="checkbox" id="agreePolicy" required>
                            <label class="form-check-label" for="agreePolicy">
                                أوافق على <a href="#" class="text-decoration-none">سياسة الخصوصية</a>
                            </label>
                        </div>
                        
                        <button type="submit" class="btn btn-success btn-lg w-100">
                            <i class="fas fa-rocket"></i> إنشاء المتجر
                        </button>
                    </div>
                </form>
            </div>
            
            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Progress Indicator -->
                <div class="progress-indicator">
                    <h5 class="mb-3">خطوات إنشاء المتجر</h5>
                    
                    <div class="step-item completed">
                        <div class="step-number">1</div>
                        <div>
                            <strong>إنشاء الحساب</strong>
                            <br><small>تم بنجاح</small>
                        </div>
                    </div>
                    
                    <div class="step-item active">
                        <div class="step-number">2</div>
                        <div>
                            <strong>معلومات المتجر</strong>
                            <br><small>قيد التعبئة</small>
                        </div>
                    </div>
                    
                    <div class="step-item">
                        <div class="step-number">3</div>
                        <div>
                            <strong>مراجعة الطلب</strong>
                            <br><small>في انتظار المراجعة</small>
                        </div>
                    </div>
                    
                    <div class="step-item">
                        <div class="step-number">4</div>
                        <div>
                            <strong>الموافقة والتفعيل</strong>
                            <br><small>خلال 24-48 ساعة</small>
                        </div>
                    </div>
                </div>

                <!-- Features -->
                <div class="form-section">
                    <h5 class="mb-3">مميزات انضمامك لمنصتنا</h5>
                    
                    <div class="feature-card">
                        <i class="fas fa-users"></i>
                        <h6>قاعدة عملاء واسعة</h6>
                        <small>وصول لآلاف العملاء المحتملين</small>
                    </div>
                    
                    <div class="feature-card">
                        <i class="fas fa-credit-card"></i>
                        <h6>نظام دفع آمن</h6>
                        <small>دفع إلكتروني آمن ومضمون</small>
                    </div>
                    
                    <div class="feature-card">
                        <i class="fas fa-chart-line"></i>
                        <h6>تقارير مفصلة</h6>
                        <small>تحليلات مبيعات وإحصائيات</small>
                    </div>
                    
                    <div class="feature-card">
                        <i class="fas fa-headset"></i>
                        <h6>دعم فني 24/7</h6>
                        <small>فريق دعم متخصص</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Terms Modal -->
    <div class="modal fade" id="termsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">شروط وأحكام البائعين</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <h6>1. التزامات البائع</h6>
                    <ul>
                        <li>تقديم منتجات أصلية وعالية الجودة</li>
                        <li>وصف دقيق للمنتجات</li>
                        <li>الالتزام بمواعيد التسليم</li>
                        <li>خدمة عملاء ممتازة</li>
                    </ul>
                    
                    <h6>2. نظام العمولة</h6>
                    <ul>
                        <li>عمولة 5% من قيمة كل عملية بيع</li>
                        <li>يتم خصم العمولة تلقائياً</li>
                        <li>دفع أرباح البائع كل أسبوعين</li>
                    </ul>
                    
                    <h6>3. سياسة الإرجاع</h6>
                    <ul>
                        <li>قبول الإرجاع خلال 14 يوم</li>
                        <li>المنتج في حالة جيدة</li>
                        <li>تحمل تكاليف الشحن حسب السبب</li>
                    </ul>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container text-center">
            <p class="mb-0">&copy; 2024 السوق الإلكتروني. جميع الحقوق محفوظة.</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function previewLogo(event) {
            const file = event.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('logoPreview').innerHTML = 
                        `<img src="${e.target.result}" class="logo-preview" alt="Store Logo">`;
                };
                reader.readAsDataURL(file);
            }
        }

        function previewBanner(event) {
            const file = event.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('bannerPreview').innerHTML = 
                        `<img src="${e.target.result}" class="banner-preview" alt="Store Banner">`;
                };
                reader.readAsDataURL(file);
            }
        }

        document.getElementById('storeForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Validate required fields
            const requiredFields = this.querySelectorAll('[required]');
            let isValid = true;
            
            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    field.focus();
                    isValid = false;
                    return false;
                }
            });
            
            if (!isValid) {
                alert('يرجى ملء جميع الحقول المطلوبة');
                return;
            }
            
            // Check agreements
            if (!document.getElementById('agreeTerms').checked ||
                !document.getElementById('agreeCommission').checked ||
                !document.getElementById('agreePolicy').checked) {
                alert('يجب الموافقة على جميع الشروط والأحكام');
                return;
            }
            
            // Simulate form submission
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري إنشاء المتجر...';
            submitBtn.disabled = true;
            
            setTimeout(() => {
                alert('تم إرسال طلب إنشاء المتجر بنجاح!\nسيتم مراجعة طلبك والرد عليك خلال 24-48 ساعة.');
                window.location.href = '/profile/';
            }, 3000);
        });

        // Auto-generate store slug from name
        document.getElementById('storeName').addEventListener('input', function() {
            const name = this.value;
            // You can add slug generation logic here
        });
    </script>
</body>
</html>
