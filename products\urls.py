from django.urls import path
from . import views

app_name = 'products'

urlpatterns = [
    # Categories
    path('categories/', views.ProductCategoryListView.as_view(), name='category-list'),
    
    # Products
    path('', views.ProductListView.as_view(), name='product-list'),
    path('featured/', views.featured_products, name='featured-products'),
    path('search/', views.search_products, name='search-products'),
    path('create/', views.ProductCreateView.as_view(), name='product-create'),
    path('<slug:slug>/', views.ProductDetailView.as_view(), name='product-detail'),
    
    # Reviews
    path('<int:product_id>/reviews/', views.ProductReviewListCreateView.as_view(), name='product-reviews'),
]
