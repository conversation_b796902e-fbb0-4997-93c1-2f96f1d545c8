# Generated by Django 5.2.4 on 2025-07-16 23:02

import django.core.validators
import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('orders', '0001_initial'),
        ('stores', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Payment',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('payment_method', models.CharField(choices=[('stripe', 'Stripe'), ('paypal', 'PayPal'), ('bank_transfer', 'تحويل بنكي'), ('cash_on_delivery', 'الدفع عند الاستلام')], max_length=20, verbose_name='طريقة الدفع')),
                ('status', models.CharField(choices=[('pending', 'في انتظار الدفع'), ('processing', 'قيد المعالجة'), ('completed', 'مكتمل'), ('failed', 'فشل'), ('cancelled', 'ملغي'), ('refunded', 'مُسترد')], default='pending', max_length=20, verbose_name='حالة الدفع')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=12, validators=[django.core.validators.MinValueValidator(0)], verbose_name='المبلغ')),
                ('currency', models.CharField(default='SAR', max_length=3, verbose_name='العملة')),
                ('gateway_transaction_id', models.CharField(blank=True, max_length=200, null=True, verbose_name='معرف المعاملة في البوابة')),
                ('gateway_response', models.JSONField(blank=True, default=dict, verbose_name='استجابة البوابة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('failure_reason', models.TextField(blank=True, null=True, verbose_name='سبب الفشل')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('completed_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الإكمال')),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments', to='orders.order', verbose_name='الطلب')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'دفعة',
                'verbose_name_plural': 'المدفوعات',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Commission',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('order_amount', models.DecimalField(decimal_places=2, max_digits=12, verbose_name='مبلغ الطلب')),
                ('commission_rate', models.DecimalField(decimal_places=4, max_digits=5, verbose_name='معدل العمولة')),
                ('commission_amount', models.DecimalField(decimal_places=2, max_digits=12, verbose_name='مبلغ العمولة')),
                ('store_amount', models.DecimalField(decimal_places=2, max_digits=12, verbose_name='مبلغ المتجر')),
                ('status', models.CharField(choices=[('pending', 'في انتظار الدفع'), ('paid', 'مدفوع'), ('cancelled', 'ملغي')], default='pending', max_length=20, verbose_name='حالة العمولة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('paid_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الدفع')),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='commissions', to='orders.order', verbose_name='الطلب')),
                ('store', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='commissions', to='stores.store', verbose_name='المتجر')),
                ('payment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='commissions', to='payments.payment', verbose_name='الدفعة')),
            ],
            options={
                'verbose_name': 'عمولة',
                'verbose_name_plural': 'العمولات',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Payout',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=12, validators=[django.core.validators.MinValueValidator(0)], verbose_name='المبلغ')),
                ('currency', models.CharField(default='SAR', max_length=3, verbose_name='العملة')),
                ('status', models.CharField(choices=[('pending', 'في انتظار المعالجة'), ('processing', 'قيد المعالجة'), ('completed', 'مكتمل'), ('failed', 'فشل'), ('cancelled', 'ملغي')], default='pending', max_length=20, verbose_name='الحالة')),
                ('bank_name', models.CharField(max_length=200, verbose_name='اسم البنك')),
                ('account_number', models.CharField(max_length=50, verbose_name='رقم الحساب')),
                ('account_holder_name', models.CharField(max_length=200, verbose_name='اسم صاحب الحساب')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('failure_reason', models.TextField(blank=True, null=True, verbose_name='سبب الفشل')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('processed_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ المعالجة')),
                ('store', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payouts', to='stores.store', verbose_name='المتجر')),
            ],
            options={
                'verbose_name': 'دفعة متجر',
                'verbose_name_plural': 'دفعات المتاجر',
                'ordering': ['-created_at'],
            },
        ),
    ]
