# Django Filter translation.
# Copyright (C) 2013
# This file is distributed under the same license as the django_filter package.
# <PERSON><PERSON><PERSON>, 2015.
#
msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-02-10 11:07+0000\n"
"PO-Revision-Date: 2015-10-11 20:53-0300\n"
"Last-Translator: <PERSON><PERSON><PERSON>\n"
"Language-Team: Spanish (Argentina)\n"
"Language: es_AR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 1.6.10\n"

#: conf.py:16
#, fuzzy
#| msgid "Any date"
msgid "date"
msgstr "Cualquier fecha"

#: conf.py:17
#, fuzzy
#| msgid "This year"
msgid "year"
msgstr "Este año"

#: conf.py:18
#, fuzzy
#| msgid "This month"
msgid "month"
msgstr "Este mes"

#: conf.py:19
#, fuzzy
#| msgid "Today"
msgid "day"
msgstr "Hoy"

#: conf.py:20
msgid "week day"
msgstr ""

#: conf.py:21
msgid "hour"
msgstr ""

#: conf.py:22
msgid "minute"
msgstr ""

#: conf.py:23
msgid "second"
msgstr ""

#: conf.py:27 conf.py:28
msgid "contains"
msgstr ""

#: conf.py:29
msgid "is in"
msgstr ""

#: conf.py:30
msgid "is greater than"
msgstr ""

#: conf.py:31
msgid "is greater than or equal to"
msgstr ""

#: conf.py:32
msgid "is less than"
msgstr ""

#: conf.py:33
msgid "is less than or equal to"
msgstr ""

#: conf.py:34 conf.py:35
msgid "starts with"
msgstr ""

#: conf.py:36 conf.py:37
msgid "ends with"
msgstr ""

#: conf.py:38
msgid "is in range"
msgstr ""

#: conf.py:39
msgid "is null"
msgstr ""

#: conf.py:40 conf.py:41
msgid "matches regex"
msgstr ""

#: conf.py:42 conf.py:49
msgid "search"
msgstr ""

#: conf.py:44
msgid "is contained by"
msgstr ""

#: conf.py:45
msgid "overlaps"
msgstr ""

#: conf.py:46
msgid "has key"
msgstr ""

#: conf.py:47
msgid "has keys"
msgstr ""

#: conf.py:48
msgid "has any keys"
msgstr ""

#: fields.py:94
msgid "Select a lookup."
msgstr ""

#: fields.py:198
msgid "Range query expects two values."
msgstr ""

#: filters.py:437
msgid "Today"
msgstr "Hoy"

#: filters.py:438
msgid "Yesterday"
msgstr ""

#: filters.py:439
msgid "Past 7 days"
msgstr "Últimos 7 días"

#: filters.py:440
msgid "This month"
msgstr "Este mes"

#: filters.py:441
msgid "This year"
msgstr "Este año"

#: filters.py:543
msgid "Multiple values may be separated by commas."
msgstr ""

#: filters.py:721
#, python-format
msgid "%s (descending)"
msgstr ""

#: filters.py:737
msgid "Ordering"
msgstr ""

#: rest_framework/filterset.py:33
#: templates/django_filters/rest_framework/form.html:5
msgid "Submit"
msgstr ""

#: templates/django_filters/rest_framework/crispy_form.html:4
#: templates/django_filters/rest_framework/form.html:2
msgid "Field filters"
msgstr ""

#: utils.py:308
msgid "exclude"
msgstr ""

#: widgets.py:58
msgid "All"
msgstr "Todos"

#: widgets.py:162
msgid "Unknown"
msgstr ""

#: widgets.py:162
msgid "Yes"
msgstr ""

#: widgets.py:162
msgid "No"
msgstr ""

#~ msgid "This is an exclusion filter"
#~ msgstr "Este es un filtro de exclusión"
