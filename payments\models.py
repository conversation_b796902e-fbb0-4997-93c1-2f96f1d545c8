from django.db import models
from django.conf import settings
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator
from orders.models import Order
from stores.models import Store
import uuid


class Payment(models.Model):
    """
    المدفوعات
    """
    STATUS_CHOICES = [
        ('pending', _('في انتظار الدفع')),
        ('processing', _('قيد المعالجة')),
        ('completed', _('مكتمل')),
        ('failed', _('فشل')),
        ('cancelled', _('ملغي')),
        ('refunded', _('مُسترد')),
    ]

    METHOD_CHOICES = [
        ('stripe', _('Stripe')),
        ('paypal', _('PayPal')),
        ('bank_transfer', _('تحويل بنكي')),
        ('cash_on_delivery', _('الدفع عند الاستلام')),
    ]

    id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False
    )
    order = models.ForeignKey(
        Order,
        on_delete=models.CASCADE,
        related_name='payments',
        verbose_name=_('الطلب')
    )
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='payments',
        verbose_name=_('المستخدم')
    )
    payment_method = models.CharField(
        max_length=20,
        choices=METHOD_CHOICES,
        verbose_name=_('طريقة الدفع')
    )
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        verbose_name=_('حالة الدفع')
    )
    amount = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        validators=[MinValueValidator(0)],
        verbose_name=_('المبلغ')
    )
    currency = models.CharField(
        max_length=3,
        default='SAR',
        verbose_name=_('العملة')
    )

    # Payment gateway specific fields
    gateway_transaction_id = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        verbose_name=_('معرف المعاملة في البوابة')
    )
    gateway_response = models.JSONField(
        default=dict,
        blank=True,
        verbose_name=_('استجابة البوابة')
    )

    # Additional information
    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('ملاحظات')
    )
    failure_reason = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('سبب الفشل')
    )

    # Timestamps
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )
    completed_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('تاريخ الإكمال')
    )

    class Meta:
        verbose_name = _('دفعة')
        verbose_name_plural = _('المدفوعات')
        ordering = ['-created_at']

    def __str__(self):
        return f"دفعة {self.amount} {self.currency} - طلب #{self.order.order_number}"

    @property
    def is_successful(self):
        return self.status == 'completed'

    @property
    def is_pending(self):
        return self.status == 'pending'

    @property
    def is_failed(self):
        return self.status == 'failed'


class Commission(models.Model):
    """
    العمولات
    """
    STATUS_CHOICES = [
        ('pending', _('في انتظار الدفع')),
        ('paid', _('مدفوع')),
        ('cancelled', _('ملغي')),
    ]

    id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False
    )
    order = models.ForeignKey(
        Order,
        on_delete=models.CASCADE,
        related_name='commissions',
        verbose_name=_('الطلب')
    )
    store = models.ForeignKey(
        Store,
        on_delete=models.CASCADE,
        related_name='commissions',
        verbose_name=_('المتجر')
    )
    payment = models.ForeignKey(
        Payment,
        on_delete=models.CASCADE,
        related_name='commissions',
        verbose_name=_('الدفعة')
    )

    # Commission details
    order_amount = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        verbose_name=_('مبلغ الطلب')
    )
    commission_rate = models.DecimalField(
        max_digits=5,
        decimal_places=4,
        verbose_name=_('معدل العمولة')
    )
    commission_amount = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        verbose_name=_('مبلغ العمولة')
    )
    store_amount = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        verbose_name=_('مبلغ المتجر')
    )

    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        verbose_name=_('حالة العمولة')
    )

    # Timestamps
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )
    paid_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('تاريخ الدفع')
    )

    class Meta:
        verbose_name = _('عمولة')
        verbose_name_plural = _('العمولات')
        ordering = ['-created_at']

    def __str__(self):
        return f"عمولة {self.commission_amount} - {self.store.name}"

    def save(self, *args, **kwargs):
        if not self.store_amount:
            self.store_amount = self.order_amount - self.commission_amount
        super().save(*args, **kwargs)


class Payout(models.Model):
    """
    دفعات المتاجر
    """
    STATUS_CHOICES = [
        ('pending', _('في انتظار المعالجة')),
        ('processing', _('قيد المعالجة')),
        ('completed', _('مكتمل')),
        ('failed', _('فشل')),
        ('cancelled', _('ملغي')),
    ]

    id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False
    )
    store = models.ForeignKey(
        Store,
        on_delete=models.CASCADE,
        related_name='payouts',
        verbose_name=_('المتجر')
    )
    amount = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        validators=[MinValueValidator(0)],
        verbose_name=_('المبلغ')
    )
    currency = models.CharField(
        max_length=3,
        default='SAR',
        verbose_name=_('العملة')
    )
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        verbose_name=_('الحالة')
    )

    # Bank details
    bank_name = models.CharField(
        max_length=200,
        verbose_name=_('اسم البنك')
    )
    account_number = models.CharField(
        max_length=50,
        verbose_name=_('رقم الحساب')
    )
    account_holder_name = models.CharField(
        max_length=200,
        verbose_name=_('اسم صاحب الحساب')
    )

    # Additional information
    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('ملاحظات')
    )
    failure_reason = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('سبب الفشل')
    )

    # Timestamps
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )
    processed_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('تاريخ المعالجة')
    )

    class Meta:
        verbose_name = _('دفعة متجر')
        verbose_name_plural = _('دفعات المتاجر')
        ordering = ['-created_at']

    def __str__(self):
        return f"دفعة {self.amount} {self.currency} - {self.store.name}"
