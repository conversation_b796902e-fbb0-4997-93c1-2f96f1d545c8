from django.shortcuts import render
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.db.models import Count, Sum, Avg, F, Q
from django.db import models
from django.utils import timezone
from datetime import timedelta
from accounts.models import User
from stores.models import Store
from products.models import Product
from orders.models import Order
from payments.models import Payment, Commission


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def admin_dashboard(request):
    """
    لوحة تحكم المدراء
    """
    user = request.user
    if not (user.is_admin_user or user.is_staff):
        return Response({'error': 'غير مصرح'}, status=403)

    # Date ranges
    today = timezone.now().date()
    last_30_days = today - timedelta(days=30)
    last_7_days = today - timedelta(days=7)

    # Users statistics
    total_users = User.objects.count()
    new_users_30_days = User.objects.filter(date_joined__gte=last_30_days).count()
    customers_count = User.objects.filter(user_type='customer').count()
    vendors_count = User.objects.filter(user_type='vendor').count()

    # Stores statistics
    total_stores = Store.objects.count()
    approved_stores = Store.objects.filter(status='approved').count()
    pending_stores = Store.objects.filter(status='pending').count()

    # Products statistics
    total_products = Product.objects.count()
    active_products = Product.objects.filter(status='active').count()
    featured_products = Product.objects.filter(is_featured=True).count()

    # Orders statistics
    total_orders = Order.objects.count()
    orders_30_days = Order.objects.filter(created_at__gte=last_30_days).count()
    orders_7_days = Order.objects.filter(created_at__gte=last_7_days).count()

    # Revenue statistics
    total_revenue = Order.objects.filter(
        status__in=['delivered', 'shipped']
    ).aggregate(total=Sum('total_amount'))['total'] or 0

    revenue_30_days = Order.objects.filter(
        created_at__gte=last_30_days,
        status__in=['delivered', 'shipped']
    ).aggregate(total=Sum('total_amount'))['total'] or 0

    # Commission statistics
    total_commission = Commission.objects.filter(
        status='paid'
    ).aggregate(total=Sum('commission_amount'))['total'] or 0

    commission_30_days = Commission.objects.filter(
        created_at__gte=last_30_days,
        status='paid'
    ).aggregate(total=Sum('commission_amount'))['total'] or 0

    # Orders by status
    orders_by_status = Order.objects.values('status').annotate(
        count=Count('id')
    ).order_by('status')

    # Recent orders
    recent_orders = Order.objects.select_related('user').order_by('-created_at')[:10]
    recent_orders_data = [{
        'id': str(order.id),
        'order_number': order.order_number,
        'user': order.user.username,
        'status': order.get_status_display(),
        'total_amount': order.total_amount,
        'created_at': order.created_at
    } for order in recent_orders]

    # Top selling products
    top_products = Product.objects.filter(
        total_sales__gt=0
    ).order_by('-total_sales')[:10]

    top_products_data = [{
        'id': product.id,
        'name': product.name,
        'store': product.store.name,
        'total_sales': product.total_sales,
        'price': product.price
    } for product in top_products]

    return Response({
        'users': {
            'total': total_users,
            'new_30_days': new_users_30_days,
            'customers': customers_count,
            'vendors': vendors_count
        },
        'stores': {
            'total': total_stores,
            'approved': approved_stores,
            'pending': pending_stores
        },
        'products': {
            'total': total_products,
            'active': active_products,
            'featured': featured_products
        },
        'orders': {
            'total': total_orders,
            'last_30_days': orders_30_days,
            'last_7_days': orders_7_days,
            'by_status': list(orders_by_status)
        },
        'revenue': {
            'total': total_revenue,
            'last_30_days': revenue_30_days
        },
        'commission': {
            'total': total_commission,
            'last_30_days': commission_30_days
        },
        'recent_orders': recent_orders_data,
        'top_products': top_products_data
    })


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def vendor_dashboard_detailed(request):
    """
    لوحة تحكم مفصلة للمتاجر
    """
    user = request.user
    if not user.is_vendor:
        return Response({'error': 'غير مصرح'}, status=403)

    store = user.stores.filter(status='approved').first()
    if not store:
        return Response({'error': 'لا يوجد متجر مُوافق عليه'}, status=404)

    # Date ranges
    today = timezone.now().date()
    last_30_days = today - timedelta(days=30)
    last_7_days = today - timedelta(days=7)

    # Products statistics
    total_products = store.products.count()
    active_products = store.products.filter(status='active').count()
    low_stock_products = store.products.filter(
        stock_quantity__lte=models.F('low_stock_threshold')
    ).count()

    # Orders statistics
    orders = Order.objects.filter(items__product__store=store).distinct()
    total_orders = orders.count()
    orders_30_days = orders.filter(created_at__gte=last_30_days).count()
    orders_7_days = orders.filter(created_at__gte=last_7_days).count()

    # Revenue statistics
    from django.db.models import F
    total_revenue = orders.filter(
        status__in=['delivered', 'shipped']
    ).aggregate(
        total=Sum('items__total_price', filter=models.Q(items__product__store=store))
    )['total'] or 0

    revenue_30_days = orders.filter(
        created_at__gte=last_30_days,
        status__in=['delivered', 'shipped']
    ).aggregate(
        total=Sum('items__total_price', filter=models.Q(items__product__store=store))
    )['total'] or 0

    # Commission statistics
    commissions = Commission.objects.filter(store=store)
    total_commission_paid = commissions.filter(status='paid').aggregate(
        total=Sum('commission_amount')
    )['total'] or 0

    pending_commission = commissions.filter(status='pending').aggregate(
        total=Sum('commission_amount')
    )['total'] or 0

    # Top selling products
    top_products = store.products.filter(
        total_sales__gt=0
    ).order_by('-total_sales')[:10]

    top_products_data = [{
        'id': product.id,
        'name': product.name,
        'total_sales': product.total_sales,
        'price': product.price,
        'stock_quantity': product.stock_quantity
    } for product in top_products]

    # Recent orders
    recent_orders = orders.select_related('user').order_by('-created_at')[:10]
    recent_orders_data = [{
        'id': str(order.id),
        'order_number': order.order_number,
        'user': order.user.username,
        'status': order.get_status_display(),
        'created_at': order.created_at,
        'items_count': order.items.filter(product__store=store).count()
    } for order in recent_orders]

    return Response({
        'store': {
            'id': store.id,
            'name': store.name,
            'status': store.get_status_display(),
            'commission_rate': store.commission_rate,
            'rating': store.rating
        },
        'products': {
            'total': total_products,
            'active': active_products,
            'low_stock': low_stock_products
        },
        'orders': {
            'total': total_orders,
            'last_30_days': orders_30_days,
            'last_7_days': orders_7_days
        },
        'revenue': {
            'total': total_revenue,
            'last_30_days': revenue_30_days
        },
        'commission': {
            'total_paid': total_commission_paid,
            'pending': pending_commission
        },
        'top_products': top_products_data,
        'recent_orders': recent_orders_data
    })
