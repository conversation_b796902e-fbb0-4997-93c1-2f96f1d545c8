from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from .models import Cart, CartItem, Order, OrderItem, OrderStatusHistory


class CartItemInline(admin.TabularInline):
    model = CartItem
    extra = 0
    readonly_fields = ('total_price', 'commission_amount')


class OrderItemInline(admin.TabularInline):
    model = OrderItem
    extra = 0
    readonly_fields = ('total_price', 'store_amount')


class OrderStatusHistoryInline(admin.TabularInline):
    model = OrderStatusHistory
    extra = 0
    readonly_fields = ('created_at',)


@admin.register(Cart)
class CartAdmin(admin.ModelAdmin):
    list_display = ('user', 'total_items', 'subtotal', 'created_at', 'updated_at')
    list_filter = ('created_at', 'updated_at')
    search_fields = ('user__username', 'user__email')
    readonly_fields = ('total_items', 'subtotal', 'total_commission', 'total_price', 'created_at', 'updated_at')
    inlines = [CartItemInline]

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user')


@admin.register(Order)
class OrderAdmin(admin.ModelAdmin):
    list_display = ('order_number', 'user', 'status', 'total_amount', 'total_commission', 'created_at')
    list_filter = ('status', 'created_at', 'shipped_at', 'delivered_at')
    search_fields = ('order_number', 'user__username', 'user__email', 'billing_email')
    ordering = ('-created_at',)
    readonly_fields = ('id', 'created_at', 'updated_at')
    inlines = [OrderItemInline, OrderStatusHistoryInline]

    fieldsets = (
        (_('معلومات الطلب'), {
            'fields': ('id', 'order_number', 'user', 'status', 'notes', 'tracking_number')
        }),
        (_('معلومات الفاتورة'), {
            'fields': ('billing_first_name', 'billing_last_name', 'billing_email', 'billing_phone',
                      'billing_address', 'billing_city', 'billing_postal_code', 'billing_country')
        }),
        (_('معلومات الشحن'), {
            'fields': ('shipping_first_name', 'shipping_last_name', 'shipping_phone',
                      'shipping_address', 'shipping_city', 'shipping_postal_code', 'shipping_country')
        }),
        (_('المبالغ'), {
            'fields': ('subtotal', 'shipping_cost', 'tax_amount', 'total_commission', 'total_amount')
        }),
        (_('التواريخ'), {
            'fields': ('created_at', 'updated_at', 'shipped_at', 'delivered_at'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user')
