<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طلباتي - السوق الإلكتروني</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .order-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s ease;
        }
        .order-card:hover {
            transform: translateY(-2px);
        }
        .order-header {
            border-bottom: 1px solid #eee;
            padding-bottom: 15px;
            margin-bottom: 15px;
        }
        .status-badge {
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: bold;
        }
        .status-pending { background: #fff3cd; color: #856404; }
        .status-paid { background: #d4edda; color: #155724; }
        .status-processing { background: #cce5ff; color: #004085; }
        .status-shipped { background: #e2e3e5; color: #383d41; }
        .status-delivered { background: #d1ecf1; color: #0c5460; }
        .status-cancelled { background: #f8d7da; color: #721c24; }
        
        .order-item {
            border-bottom: 1px solid #f0f0f0;
            padding: 15px 0;
        }
        .order-item:last-child {
            border-bottom: none;
        }
        .product-image {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 8px;
        }
        .order-total {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-top: 15px;
        }
        .filter-tabs {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        .tracking-timeline {
            position: relative;
            padding: 20px 0;
        }
        .timeline-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        .timeline-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 15px;
        }
        .timeline-content {
            flex: 1;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-store"></i> السوق الإلكتروني
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/products/">المنتجات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/stores/">المتاجر</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="/cart/">
                            <i class="fas fa-shopping-cart"></i> السلة <span class="badge bg-primary">0</span>
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle active" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> حسابي
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/profile/">الملف الشخصي</a></li>
                            <li><a class="dropdown-item active" href="/orders/">طلباتي</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/logout/">تسجيل الخروج</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h2 class="mb-4">
                    <i class="fas fa-shopping-bag"></i> طلباتي
                </h2>
            </div>
        </div>

        <!-- Filter Tabs -->
        <div class="filter-tabs">
            <ul class="nav nav-pills" id="orderTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="all-tab" data-bs-toggle="pill" data-bs-target="#all" 
                            type="button" role="tab" onclick="filterOrders('all')">
                        جميع الطلبات
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="pending-tab" data-bs-toggle="pill" data-bs-target="#pending" 
                            type="button" role="tab" onclick="filterOrders('pending')">
                        في انتظار الدفع
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="processing-tab" data-bs-toggle="pill" data-bs-target="#processing" 
                            type="button" role="tab" onclick="filterOrders('processing')">
                        قيد المعالجة
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="shipped-tab" data-bs-toggle="pill" data-bs-target="#shipped" 
                            type="button" role="tab" onclick="filterOrders('shipped')">
                        تم الشحن
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="delivered-tab" data-bs-toggle="pill" data-bs-target="#delivered" 
                            type="button" role="tab" onclick="filterOrders('delivered')">
                        تم التسليم
                    </button>
                </li>
            </ul>
        </div>

        <!-- Orders List -->
        <div id="ordersList">
            <!-- Orders will be loaded here -->
        </div>
    </div>

    <!-- Order Details Modal -->
    <div class="modal fade" id="orderDetailsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تفاصيل الطلب</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="orderDetailsContent">
                    <!-- Order details will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container text-center">
            <p class="mb-0">&copy; 2024 السوق الإلكتروني. جميع الحقوق محفوظة.</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Sample orders data
        const ordersData = [
            {
                id: '12345678-1234-1234-1234-123456789012',
                order_number: '1234567890',
                status: 'delivered',
                status_display: 'تم التسليم',
                total_amount: 9298.00,
                created_at: '2024-01-15T10:30:00Z',
                items: [
                    {
                        product_name: 'iPhone 15 Pro',
                        store_name: 'متجر التقنية الحديثة',
                        quantity: 1,
                        price: 4999.00,
                        image: '/static/images/iphone15.jpg'
                    },
                    {
                        product_name: 'Samsung Galaxy S24 Ultra',
                        store_name: 'متجر التقنية الحديثة',
                        quantity: 1,
                        price: 4299.00,
                        image: '/static/images/samsung-s24.jpg'
                    }
                ],
                tracking_number: 'TRK123456789'
            },
            {
                id: '12345678-1234-1234-1234-123456789013',
                order_number: '1234567891',
                status: 'processing',
                status_display: 'قيد المعالجة',
                total_amount: 899.00,
                created_at: '2024-01-20T14:15:00Z',
                items: [
                    {
                        product_name: 'AirPods Pro 3',
                        store_name: 'متجر التقنية الحديثة',
                        quantity: 1,
                        price: 899.00,
                        image: '/static/images/airpods.jpg'
                    }
                ],
                tracking_number: null
            }
        ];

        let currentFilter = 'all';

        function loadOrders() {
            const ordersList = document.getElementById('ordersList');
            const filteredOrders = currentFilter === 'all' 
                ? ordersData 
                : ordersData.filter(order => order.status === currentFilter);

            if (filteredOrders.length === 0) {
                ordersList.innerHTML = `
                    <div class="text-center py-5">
                        <i class="fas fa-shopping-bag fa-5x text-muted mb-4"></i>
                        <h4>لا توجد طلبات</h4>
                        <p class="text-muted">لم تقم بأي طلبات بعد</p>
                        <a href="/products/" class="btn btn-primary">
                            <i class="fas fa-shopping-bag"></i> ابدأ التسوق
                        </a>
                    </div>
                `;
                return;
            }

            ordersList.innerHTML = filteredOrders.map(order => `
                <div class="order-card">
                    <div class="order-header">
                        <div class="row align-items-center">
                            <div class="col-md-3">
                                <h6 class="mb-1">طلب #${order.order_number}</h6>
                                <small class="text-muted">${formatDate(order.created_at)}</small>
                            </div>
                            <div class="col-md-3">
                                <span class="status-badge status-${order.status}">${order.status_display}</span>
                            </div>
                            <div class="col-md-3">
                                <strong class="text-primary">${order.total_amount.toFixed(2)} ر.س</strong>
                            </div>
                            <div class="col-md-3 text-end">
                                <button class="btn btn-outline-primary btn-sm" onclick="showOrderDetails('${order.id}')">
                                    <i class="fas fa-eye"></i> التفاصيل
                                </button>
                                ${order.status === 'pending' ? `
                                    <button class="btn btn-outline-danger btn-sm ms-2" onclick="cancelOrder('${order.id}')">
                                        <i class="fas fa-times"></i> إلغاء
                                    </button>
                                ` : ''}
                            </div>
                        </div>
                    </div>
                    
                    <div class="order-items">
                        ${order.items.map(item => `
                            <div class="order-item">
                                <div class="row align-items-center">
                                    <div class="col-md-1">
                                        <img src="${item.image || '/static/images/no-image.png'}" 
                                             class="product-image" alt="${item.product_name}">
                                    </div>
                                    <div class="col-md-5">
                                        <h6 class="mb-1">${item.product_name}</h6>
                                        <small class="text-muted">
                                            <i class="fas fa-store"></i> ${item.store_name}
                                        </small>
                                    </div>
                                    <div class="col-md-2">
                                        <span>الكمية: ${item.quantity}</span>
                                    </div>
                                    <div class="col-md-2">
                                        <span>${item.price.toFixed(2)} ر.س</span>
                                    </div>
                                    <div class="col-md-2 text-end">
                                        ${order.status === 'delivered' ? `
                                            <button class="btn btn-outline-warning btn-sm">
                                                <i class="fas fa-star"></i> تقييم
                                            </button>
                                        ` : ''}
                                    </div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                    
                    ${order.tracking_number ? `
                        <div class="order-total">
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>رقم التتبع: ${order.tracking_number}</strong>
                                </div>
                                <div class="col-md-6 text-end">
                                    <button class="btn btn-outline-info btn-sm" onclick="trackOrder('${order.tracking_number}')">
                                        <i class="fas fa-truck"></i> تتبع الشحنة
                                    </button>
                                </div>
                            </div>
                        </div>
                    ` : ''}
                </div>
            `).join('');
        }

        function filterOrders(status) {
            currentFilter = status;
            loadOrders();
        }

        function showOrderDetails(orderId) {
            const order = ordersData.find(o => o.id === orderId);
            if (!order) return;

            const modalContent = document.getElementById('orderDetailsContent');
            modalContent.innerHTML = `
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h6>معلومات الطلب</h6>
                        <p><strong>رقم الطلب:</strong> ${order.order_number}</p>
                        <p><strong>التاريخ:</strong> ${formatDate(order.created_at)}</p>
                        <p><strong>الحالة:</strong> <span class="status-badge status-${order.status}">${order.status_display}</span></p>
                        ${order.tracking_number ? `<p><strong>رقم التتبع:</strong> ${order.tracking_number}</p>` : ''}
                    </div>
                    <div class="col-md-6">
                        <h6>ملخص الطلب</h6>
                        <p><strong>المجموع:</strong> ${order.total_amount.toFixed(2)} ر.س</p>
                        <p><strong>عدد المنتجات:</strong> ${order.items.length}</p>
                    </div>
                </div>
                
                <h6>المنتجات</h6>
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>المنتج</th>
                                <th>المتجر</th>
                                <th>الكمية</th>
                                <th>السعر</th>
                                <th>الإجمالي</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${order.items.map(item => `
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <img src="${item.image || '/static/images/no-image.png'}" 
                                                 width="40" height="40" class="me-2 rounded">
                                            ${item.product_name}
                                        </div>
                                    </td>
                                    <td>${item.store_name}</td>
                                    <td>${item.quantity}</td>
                                    <td>${item.price.toFixed(2)} ر.س</td>
                                    <td>${(item.price * item.quantity).toFixed(2)} ر.س</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
                
                ${order.status !== 'pending' && order.status !== 'cancelled' ? `
                    <div class="tracking-timeline mt-4">
                        <h6>تتبع الطلب</h6>
                        <div class="timeline-item">
                            <div class="timeline-icon bg-success text-white">
                                <i class="fas fa-check"></i>
                            </div>
                            <div class="timeline-content">
                                <strong>تم تأكيد الطلب</strong>
                                <br><small class="text-muted">${formatDate(order.created_at)}</small>
                            </div>
                        </div>
                        ${order.status !== 'pending' ? `
                            <div class="timeline-item">
                                <div class="timeline-icon bg-info text-white">
                                    <i class="fas fa-cog"></i>
                                </div>
                                <div class="timeline-content">
                                    <strong>قيد المعالجة</strong>
                                    <br><small class="text-muted">جاري تحضير الطلب</small>
                                </div>
                            </div>
                        ` : ''}
                        ${order.status === 'shipped' || order.status === 'delivered' ? `
                            <div class="timeline-item">
                                <div class="timeline-icon bg-warning text-white">
                                    <i class="fas fa-truck"></i>
                                </div>
                                <div class="timeline-content">
                                    <strong>تم الشحن</strong>
                                    <br><small class="text-muted">رقم التتبع: ${order.tracking_number}</small>
                                </div>
                            </div>
                        ` : ''}
                        ${order.status === 'delivered' ? `
                            <div class="timeline-item">
                                <div class="timeline-icon bg-success text-white">
                                    <i class="fas fa-home"></i>
                                </div>
                                <div class="timeline-content">
                                    <strong>تم التسليم</strong>
                                    <br><small class="text-muted">تم تسليم الطلب بنجاح</small>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                ` : ''}
            `;

            new bootstrap.Modal(document.getElementById('orderDetailsModal')).show();
        }

        function cancelOrder(orderId) {
            if (confirm('هل أنت متأكد من إلغاء هذا الطلب؟')) {
                // API call to cancel order
                alert('تم إلغاء الطلب بنجاح');
                loadOrders();
            }
        }

        function trackOrder(trackingNumber) {
            alert(`تتبع الشحنة: ${trackingNumber}\nسيتم إعادة توجيهك لصفحة التتبع`);
        }

        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-SA', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        // Load orders on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadOrders();
        });
    </script>
</body>
</html>
