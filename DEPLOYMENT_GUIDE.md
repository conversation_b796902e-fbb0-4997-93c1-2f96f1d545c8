# دليل النشر والتشغيل - السوق الإلكتروني

## 🚀 التشغيل السريع (للتطوير)

### 1. الإعداد الأولي
```bash
# تشغيل سكريبت الإعداد التلقائي
python setup.py

# أو الإعداد اليدوي:
python -m venv marketplace_env
source marketplace_env/Scripts/activate  # Windows
pip install -r requirements.txt
python manage.py migrate
python create_admin.py
```

### 2. إضافة بيانات تجريبية
```bash
python create_sample_data.py
```

### 3. تشغيل الخادم
```bash
python run_server.py
# أو
python manage.py runserver
```

### 4. اختبار النظام
```bash
python test_system.py
```

## 🌐 الوصول للنظام

- **الصفحة الرئيسية**: http://127.0.0.1:8000
- **صفحة المنتجات**: http://127.0.0.1:8000/products/
- **لوحة التحكم**: http://127.0.0.1:8000/admin/
- **API المنتجات**: http://127.0.0.1:8000/api/products/
- **API الطلبات**: http://127.0.0.1:8000/api/orders/

## 👥 بيانات الدخول الافتراضية

### المدير
- **المستخدم**: admin
- **كلمة المرور**: admin123

### صاحب متجر (بعد تشغيل create_sample_data.py)
- **المستخدم**: vendor1
- **كلمة المرور**: vendor123

### عميل (بعد تشغيل create_sample_data.py)
- **المستخدم**: customer1
- **كلمة المرور**: customer123

## 🐳 النشر باستخدام Docker

### 1. إعداد متغيرات البيئة
```bash
# إنشاء ملف .env للإنتاج
cp .env .env.production

# تحديث المتغيرات:
SECRET_KEY=your-production-secret-key
DEBUG=False
ALLOWED_HOSTS=your-domain.com,www.your-domain.com
DB_PASSWORD=strong-database-password
STRIPE_SECRET_KEY=your-stripe-secret-key
```

### 2. بناء وتشغيل الحاويات
```bash
# بناء الصور
docker-compose build

# تشغيل الخدمات
docker-compose up -d

# تطبيق الهجرات
docker-compose exec web python manage.py migrate

# إنشاء مستخدم إداري
docker-compose exec web python create_admin.py

# جمع الملفات الثابتة
docker-compose exec web python manage.py collectstatic --noinput
```

### 3. مراقبة الخدمات
```bash
# عرض حالة الخدمات
docker-compose ps

# عرض السجلات
docker-compose logs -f web

# إيقاف الخدمات
docker-compose down
```

## ☁️ النشر على الخادم

### 1. متطلبات الخادم
- Ubuntu 20.04+ أو CentOS 8+
- Python 3.10+
- PostgreSQL 13+
- Redis 6+
- Nginx
- SSL Certificate

### 2. إعداد قاعدة البيانات
```sql
-- إنشاء قاعدة البيانات والمستخدم
CREATE DATABASE marketplace_db;
CREATE USER marketplace_user WITH PASSWORD 'strong_password';
GRANT ALL PRIVILEGES ON DATABASE marketplace_db TO marketplace_user;
```

### 3. إعداد البيئة
```bash
# إنشاء مستخدم النظام
sudo adduser marketplace
sudo usermod -aG sudo marketplace

# إعداد المشروع
cd /var/www/
sudo git clone your-repo-url marketplace
sudo chown -R marketplace:marketplace marketplace
cd marketplace

# إعداد البيئة الافتراضية
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
pip install gunicorn psycopg2-binary
```

### 4. إعداد Gunicorn
```bash
# إنشاء ملف خدمة systemd
sudo nano /etc/systemd/system/marketplace.service
```

```ini
[Unit]
Description=Marketplace Django App
After=network.target

[Service]
User=marketplace
Group=marketplace
WorkingDirectory=/var/www/marketplace
Environment="PATH=/var/www/marketplace/venv/bin"
ExecStart=/var/www/marketplace/venv/bin/gunicorn --workers 3 --bind unix:/var/www/marketplace/marketplace.sock marketplace.wsgi:application
Restart=always

[Install]
WantedBy=multi-user.target
```

```bash
# تفعيل الخدمة
sudo systemctl daemon-reload
sudo systemctl start marketplace
sudo systemctl enable marketplace
```

### 5. إعداد Nginx
```bash
# نسخ ملف التكوين
sudo cp nginx.conf /etc/nginx/sites-available/marketplace
sudo ln -s /etc/nginx/sites-available/marketplace /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

### 6. إعداد SSL
```bash
# باستخدام Let's Encrypt
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com -d www.your-domain.com
```

## 🔧 إعدادات الإنتاج

### 1. متغيرات البيئة المطلوبة
```env
SECRET_KEY=your-very-secure-secret-key
DEBUG=False
ALLOWED_HOSTS=your-domain.com,www.your-domain.com

# قاعدة البيانات
DB_NAME=marketplace_db
DB_USER=marketplace_user
DB_PASSWORD=secure_password
DB_HOST=localhost
DB_PORT=5432

# Redis
REDIS_URL=redis://127.0.0.1:6379/1
CELERY_BROKER_URL=redis://127.0.0.1:6379/0

# البريد الإلكتروني
EMAIL_HOST=smtp.gmail.com
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password

# Stripe
STRIPE_PUBLISHABLE_KEY=pk_live_...
STRIPE_SECRET_KEY=sk_live_...

# AWS S3 (اختياري)
USE_S3=True
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_STORAGE_BUCKET_NAME=your-bucket-name
```

### 2. إعداد Celery للمهام الخلفية
```bash
# إنشاء خدمة Celery Worker
sudo nano /etc/systemd/system/celery.service
```

```ini
[Unit]
Description=Celery Service
After=network.target

[Service]
Type=forking
User=marketplace
Group=marketplace
EnvironmentFile=/var/www/marketplace/.env
WorkingDirectory=/var/www/marketplace
ExecStart=/var/www/marketplace/venv/bin/celery multi start worker1 -A marketplace --pidfile=/var/run/celery/%n.pid --logfile=/var/log/celery/%n%I.log --loglevel=INFO
ExecStop=/var/www/marketplace/venv/bin/celery multi stopwait worker1 --pidfile=/var/run/celery/%n.pid
ExecReload=/var/www/marketplace/venv/bin/celery multi restart worker1 -A marketplace --pidfile=/var/run/celery/%n.pid --logfile=/var/log/celery/%n%I.log --loglevel=INFO

[Install]
WantedBy=multi-user.target
```

## 📊 المراقبة والصيانة

### 1. مراقبة السجلات
```bash
# سجلات Django
tail -f /var/log/marketplace/django.log

# سجلات Nginx
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log

# سجلات Celery
tail -f /var/log/celery/worker1.log
```

### 2. النسخ الاحتياطي
```bash
# نسخ احتياطي لقاعدة البيانات
pg_dump marketplace_db > backup_$(date +%Y%m%d_%H%M%S).sql

# نسخ احتياطي للملفات
tar -czf media_backup_$(date +%Y%m%d_%H%M%S).tar.gz /var/www/marketplace/media/
```

### 3. التحديثات
```bash
# تحديث الكود
cd /var/www/marketplace
git pull origin main

# تطبيق الهجرات
source venv/bin/activate
python manage.py migrate

# جمع الملفات الثابتة
python manage.py collectstatic --noinput

# إعادة تشغيل الخدمات
sudo systemctl restart marketplace
sudo systemctl restart celery
```

## 🔒 الأمان

### 1. جدار الحماية
```bash
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw enable
```

### 2. تحديثات الأمان
```bash
# تحديث النظام
sudo apt update && sudo apt upgrade -y

# تحديث Python packages
pip install --upgrade -r requirements.txt
```

### 3. مراقبة الأمان
- استخدم fail2ban لحماية SSH
- راقب السجلات بانتظام
- استخدم SSL certificates صالحة
- قم بتحديث كلمات المرور بانتظام

## 📞 الدعم والاستكشاف

### مشاكل شائعة:

1. **خطأ في الاتصال بقاعدة البيانات**
   - تحقق من إعدادات قاعدة البيانات في .env
   - تأكد من تشغيل PostgreSQL

2. **ملفات CSS/JS لا تظهر**
   - تشغيل `python manage.py collectstatic`
   - تحقق من إعدادات Nginx

3. **خطأ 502 Bad Gateway**
   - تحقق من تشغيل Gunicorn
   - راجع سجلات Nginx

4. **المهام الخلفية لا تعمل**
   - تحقق من تشغيل Redis
   - تحقق من تشغيل Celery

### للحصول على المساعدة:
- راجع السجلات أولاً
- تشغيل `python test_system.py` للتشخيص
- تحقق من GitHub Issues
