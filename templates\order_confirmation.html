<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تأكيد الطلب - السوق الإلكتروني</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .success-header {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            margin-bottom: 30px;
            position: relative;
            overflow: hidden;
        }
        .success-header::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
            animation: float 20s infinite linear;
        }
        @keyframes float {
            0% { transform: translateX(0) translateY(0); }
            100% { transform: translateX(-50px) translateY(-50px); }
        }
        .success-icon {
            width: 100px;
            height: 100px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        .order-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
        }
        .timeline {
            position: relative;
            padding: 20px 0;
        }
        .timeline-item {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            position: relative;
        }
        .timeline-item::before {
            content: '';
            position: absolute;
            left: 20px;
            top: 40px;
            bottom: -20px;
            width: 2px;
            background: #e9ecef;
        }
        .timeline-item:last-child::before {
            display: none;
        }
        .timeline-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 20px;
            z-index: 1;
            position: relative;
        }
        .timeline-icon.completed {
            background: #28a745;
            color: white;
        }
        .timeline-icon.pending {
            background: #ffc107;
            color: white;
        }
        .timeline-icon.future {
            background: #e9ecef;
            color: #6c757d;
        }
        .product-item {
            display: flex;
            align-items: center;
            padding: 15px;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            margin-bottom: 10px;
        }
        .product-image {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 8px;
            margin-left: 15px;
        }
        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
        }
        .print-button {
            background: linear-gradient(135deg, #6c757d, #495057);
            border: none;
            color: white;
            padding: 12px 25px;
            border-radius: 25px;
            transition: all 0.3s ease;
        }
        .print-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-store"></i> السوق الإلكتروني
            </a>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Success Header -->
        <div class="success-header">
            <div class="success-icon">
                <i class="fas fa-check fa-3x"></i>
            </div>
            <h1 class="mb-3">تم تأكيد طلبك بنجاح!</h1>
            <p class="lead mb-0">شكراً لك على ثقتك بنا. سنقوم بمعالجة طلبك في أقرب وقت ممكن.</p>
        </div>

        <div class="row">
            <div class="col-lg-8">
                <!-- Order Details -->
                <div class="order-card">
                    <h4 class="mb-4">
                        <i class="fas fa-receipt text-primary"></i> تفاصيل الطلب
                    </h4>
                    
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6>معلومات الطلب</h6>
                            <p><strong>رقم الطلب:</strong> #1234567892</p>
                            <p><strong>تاريخ الطلب:</strong> 25 يناير 2024، 2:30 م</p>
                            <p><strong>طريقة الدفع:</strong> بطاقة ائتمان</p>
                            <p><strong>حالة الدفع:</strong> <span class="badge bg-success">مدفوع</span></p>
                        </div>
                        <div class="col-md-6">
                            <h6>معلومات الشحن</h6>
                            <p><strong>الاسم:</strong> أحمد محمد العلي</p>
                            <p><strong>العنوان:</strong> شارع الملك فهد، حي العليا، الرياض 12345</p>
                            <p><strong>الهاتف:</strong> +966 50 123 4567</p>
                            <p><strong>البريد:</strong> <EMAIL></p>
                        </div>
                    </div>
                    
                    <h6>المنتجات المطلوبة</h6>
                    <div class="product-item">
                        <img src="/static/images/iphone15.jpg" class="product-image" alt="iPhone 15 Pro">
                        <div class="flex-grow-1">
                            <h6 class="mb-1">iPhone 15 Pro</h6>
                            <small class="text-muted">متجر التقنية الحديثة</small>
                            <div class="d-flex justify-content-between mt-2">
                                <span>الكمية: 1</span>
                                <strong>4,999.00 ر.س</strong>
                            </div>
                        </div>
                    </div>
                    
                    <div class="product-item">
                        <img src="/static/images/samsung-s24.jpg" class="product-image" alt="Samsung Galaxy S24 Ultra">
                        <div class="flex-grow-1">
                            <h6 class="mb-1">Samsung Galaxy S24 Ultra</h6>
                            <small class="text-muted">متجر التقنية الحديثة</small>
                            <div class="d-flex justify-content-between mt-2">
                                <span>الكمية: 1</span>
                                <strong>4,299.00 ر.س</strong>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Order Timeline -->
                <div class="order-card">
                    <h4 class="mb-4">
                        <i class="fas fa-route text-primary"></i> تتبع الطلب
                    </h4>
                    
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-icon completed">
                                <i class="fas fa-check"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">تم تأكيد الطلب</h6>
                                <small class="text-muted">25 يناير 2024، 2:30 م</small>
                                <p class="mb-0 mt-1">تم استلام طلبك وتأكيد الدفع بنجاح</p>
                            </div>
                        </div>
                        
                        <div class="timeline-item">
                            <div class="timeline-icon pending">
                                <i class="fas fa-cog"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">قيد المعالجة</h6>
                                <small class="text-muted">متوقع خلال 24 ساعة</small>
                                <p class="mb-0 mt-1">جاري تحضير طلبك للشحن</p>
                            </div>
                        </div>
                        
                        <div class="timeline-item">
                            <div class="timeline-icon future">
                                <i class="fas fa-truck"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">تم الشحن</h6>
                                <small class="text-muted">متوقع خلال 2-3 أيام</small>
                                <p class="mb-0 mt-1">سيتم إرسال رقم التتبع عند الشحن</p>
                            </div>
                        </div>
                        
                        <div class="timeline-item">
                            <div class="timeline-icon future">
                                <i class="fas fa-home"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">تم التسليم</h6>
                                <small class="text-muted">متوقع خلال 3-5 أيام</small>
                                <p class="mb-0 mt-1">سيتم تسليم طلبك في العنوان المحدد</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4">
                <!-- Order Summary -->
                <div class="order-card">
                    <h5 class="mb-3">ملخص الطلب</h5>
                    
                    <div class="d-flex justify-content-between mb-2">
                        <span>المجموع الفرعي:</span>
                        <span>9,298.00 ر.س</span>
                    </div>
                    
                    <div class="d-flex justify-content-between mb-2">
                        <span>الشحن:</span>
                        <span class="text-success">مجاني</span>
                    </div>
                    
                    <div class="d-flex justify-content-between mb-2">
                        <span>الضريبة:</span>
                        <span>0.00 ر.س</span>
                    </div>
                    
                    <hr>
                    
                    <div class="d-flex justify-content-between mb-3">
                        <strong>الإجمالي:</strong>
                        <strong class="text-primary fs-5">9,298.00 ر.س</strong>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>معلومة مهمة:</strong>
                        سيتم إرسال تحديثات الطلب على بريدك الإلكتروني ورقم هاتفك.
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="order-card">
                    <h6 class="mb-3">إجراءات سريعة</h6>
                    
                    <div class="d-grid gap-2">
                        <a href="/orders/" class="btn btn-primary">
                            <i class="fas fa-list"></i> عرض جميع طلباتي
                        </a>
                        
                        <button class="btn btn-outline-primary" onclick="window.print()">
                            <i class="fas fa-print"></i> طباعة الفاتورة
                        </button>
                        
                        <a href="/products/" class="btn btn-outline-success">
                            <i class="fas fa-shopping-bag"></i> متابعة التسوق
                        </a>
                        
                        <a href="/support/" class="btn btn-outline-info">
                            <i class="fas fa-headset"></i> تواصل معنا
                        </a>
                    </div>
                </div>

                <!-- Recommendations -->
                <div class="order-card">
                    <h6 class="mb-3">منتجات قد تعجبك</h6>
                    
                    <div class="row">
                        <div class="col-6 mb-3">
                            <div class="card">
                                <img src="/static/images/airpods.jpg" class="card-img-top" alt="AirPods">
                                <div class="card-body p-2">
                                    <h6 class="card-title small">AirPods Pro 3</h6>
                                    <p class="card-text small text-primary">899.00 ر.س</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="card">
                                <img src="/static/images/macbook.jpg" class="card-img-top" alt="MacBook">
                                <div class="card-body p-2">
                                    <h6 class="card-title small">MacBook Pro</h6>
                                    <p class="card-text small text-primary">8,999.00 ر.س</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="action-buttons">
            <a href="/" class="btn btn-lg btn-primary">
                <i class="fas fa-home"></i> العودة للرئيسية
            </a>
            
            <a href="/products/" class="btn btn-lg btn-success">
                <i class="fas fa-shopping-bag"></i> متابعة التسوق
            </a>
            
            <button class="btn btn-lg print-button" onclick="window.print()">
                <i class="fas fa-print"></i> طباعة الفاتورة
            </button>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container text-center">
            <p class="mb-0">&copy; 2024 السوق الإلكتروني. جميع الحقوق محفوظة.</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-redirect to orders page after 30 seconds
        setTimeout(() => {
            if (confirm('هل تريد الانتقال لصفحة طلباتك؟')) {
                window.location.href = '/orders/';
            }
        }, 30000);

        // Send order confirmation email (simulation)
        setTimeout(() => {
            console.log('تم إرسال بريد تأكيد الطلب');
        }, 2000);

        // Print functionality
        function printInvoice() {
            window.print();
        }

        // Show success animation on load
        document.addEventListener('DOMContentLoaded', function() {
            // Add confetti effect or other animations here
            console.log('تم تأكيد الطلب بنجاح!');
        });
    </script>
</body>
</html>
