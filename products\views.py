from django.shortcuts import render, get_object_or_404
from rest_framework import generics, filters, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, IsAuthenticatedOrReadOnly
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q, Avg
from .models import ProductCategory, Product, ProductReview
from .serializers import (
    ProductCategorySerializer, ProductListSerializer, ProductDetailSerializer,
    ProductReviewSerializer, ProductCreateSerializer
)


class ProductCategoryListView(generics.ListAPIView):
    """
    عرض قائمة تصنيفات المنتجات
    """
    queryset = ProductCategory.objects.filter(is_active=True, parent=None)
    serializer_class = ProductCategorySerializer
    permission_classes = [IsAuthenticatedOrReadOnly]


class ProductListView(generics.ListAPIView):
    """
    عرض قائمة المنتجات مع إمكانية البحث والفلترة
    """
    serializer_class = ProductListSerializer
    permission_classes = [IsAuthenticatedOrReadOnly]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['category', 'store', 'is_featured', 'price']
    search_fields = ['name', 'description', 'tags']
    ordering_fields = ['price', 'created_at', 'rating', 'total_sales']
    ordering = ['-created_at']

    def get_queryset(self):
        queryset = Product.objects.filter(status='active').select_related('store', 'category')

        # Filter by price range
        min_price = self.request.query_params.get('min_price')
        max_price = self.request.query_params.get('max_price')

        if min_price:
            queryset = queryset.filter(price__gte=min_price)
        if max_price:
            queryset = queryset.filter(price__lte=max_price)

        # Filter by store status
        queryset = queryset.filter(store__status='approved')

        return queryset


class ProductDetailView(generics.RetrieveAPIView):
    """
    عرض تفاصيل منتج واحد
    """
    queryset = Product.objects.filter(status='active').select_related('store', 'category')
    serializer_class = ProductDetailSerializer
    permission_classes = [IsAuthenticatedOrReadOnly]
    lookup_field = 'slug'

    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        # Increment views count
        Product.objects.filter(id=instance.id).update(views_count=instance.views_count + 1)
        serializer = self.get_serializer(instance)
        return Response(serializer.data)


class ProductCreateView(generics.CreateAPIView):
    """
    إنشاء منتج جديد (للمتاجر فقط)
    """
    serializer_class = ProductCreateSerializer
    permission_classes = [IsAuthenticated]

    def perform_create(self, serializer):
        # Ensure user is a vendor with an approved store
        if not self.request.user.is_vendor:
            raise PermissionError("يجب أن تكون صاحب متجر لإضافة منتجات")

        store = self.request.user.stores.filter(status='approved').first()
        if not store:
            raise PermissionError("لا يوجد متجر مُوافق عليه")

        serializer.save(store=store)


class ProductReviewListCreateView(generics.ListCreateAPIView):
    """
    عرض وإنشاء تقييمات المنتجات
    """
    serializer_class = ProductReviewSerializer
    permission_classes = [IsAuthenticatedOrReadOnly]

    def get_queryset(self):
        product_id = self.kwargs.get('product_id')
        return ProductReview.objects.filter(
            product_id=product_id,
            is_approved=True
        ).select_related('user').order_by('-created_at')

    def perform_create(self, serializer):
        product_id = self.kwargs.get('product_id')
        product = get_object_or_404(Product, id=product_id, status='active')

        # Check if user already reviewed this product
        if ProductReview.objects.filter(product=product, user=self.request.user).exists():
            from rest_framework.exceptions import ValidationError
            raise ValidationError("لقد قمت بتقييم هذا المنتج من قبل")

        serializer.save(user=self.request.user, product=product)

        # Update product rating
        avg_rating = ProductReview.objects.filter(
            product=product,
            is_approved=True
        ).aggregate(avg_rating=Avg('rating'))['avg_rating']

        if avg_rating:
            product.rating = round(avg_rating, 2)
            product.save(update_fields=['rating'])


@api_view(['GET'])
@permission_classes([IsAuthenticatedOrReadOnly])
def featured_products(request):
    """
    عرض المنتجات المميزة
    """
    products = Product.objects.filter(
        status='active',
        is_featured=True,
        store__status='approved'
    ).select_related('store', 'category')[:12]

    serializer = ProductListSerializer(products, many=True)
    return Response(serializer.data)


@api_view(['GET'])
@permission_classes([IsAuthenticatedOrReadOnly])
def search_products(request):
    """
    البحث في المنتجات
    """
    query = request.GET.get('q', '')
    if not query:
        return Response({'results': []})

    products = Product.objects.filter(
        Q(name__icontains=query) |
        Q(description__icontains=query) |
        Q(tags__icontains=query),
        status='active',
        store__status='approved'
    ).select_related('store', 'category')[:20]

    serializer = ProductListSerializer(products, many=True)
    return Response({'results': serializer.data, 'query': query})
