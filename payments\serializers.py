from rest_framework import serializers
from .models import Payment, Commission, Payout
from orders.models import Order


class PaymentSerializer(serializers.ModelSerializer):
    order_number = serializers.CharField(source='order.order_number', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    method_display = serializers.CharField(source='get_payment_method_display', read_only=True)
    
    class Meta:
        model = Payment
        fields = [
            'id', 'order', 'order_number', 'payment_method', 'method_display',
            'status', 'status_display', 'amount', 'currency', 'gateway_transaction_id',
            'notes', 'failure_reason', 'created_at', 'completed_at'
        ]
        read_only_fields = ['id', 'gateway_transaction_id', 'created_at', 'completed_at']


class PaymentCreateSerializer(serializers.ModelSerializer):
    order_id = serializers.UUIDField(write_only=True)
    
    class Meta:
        model = Payment
        fields = ['order_id', 'payment_method']
    
    def create(self, validated_data):
        order_id = validated_data.pop('order_id')
        user = self.context['request'].user
        
        try:
            order = Order.objects.get(id=order_id, user=user, status='pending')
        except Order.DoesNotExist:
            raise serializers.ValidationError("الطلب غير موجود أو لا يمكن دفعه")
        
        # Check if payment already exists
        if Payment.objects.filter(order=order, status__in=['pending', 'processing', 'completed']).exists():
            raise serializers.ValidationError("يوجد دفعة قائمة لهذا الطلب")
        
        payment = Payment.objects.create(
            order=order,
            user=user,
            amount=order.total_amount,
            **validated_data
        )
        
        return payment


class CommissionSerializer(serializers.ModelSerializer):
    order_number = serializers.CharField(source='order.order_number', read_only=True)
    store_name = serializers.CharField(source='store.name', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = Commission
        fields = [
            'id', 'order', 'order_number', 'store', 'store_name', 'order_amount',
            'commission_rate', 'commission_amount', 'store_amount', 'status',
            'status_display', 'created_at', 'paid_at'
        ]


class PayoutSerializer(serializers.ModelSerializer):
    store_name = serializers.CharField(source='store.name', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = Payout
        fields = [
            'id', 'store', 'store_name', 'amount', 'currency', 'status',
            'status_display', 'bank_name', 'account_number', 'account_holder_name',
            'notes', 'failure_reason', 'created_at', 'processed_at'
        ]


class PayoutCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Payout
        fields = ['amount', 'bank_name', 'account_number', 'account_holder_name', 'notes']
    
    def create(self, validated_data):
        user = self.context['request'].user
        
        if not user.is_vendor:
            raise serializers.ValidationError("يجب أن تكون صاحب متجر لطلب دفعة")
        
        store = user.stores.filter(status='approved').first()
        if not store:
            raise serializers.ValidationError("لا يوجد متجر مُوافق عليه")
        
        # Check if there are pending commissions
        pending_commissions = Commission.objects.filter(
            store=store,
            status='pending'
        ).aggregate(total=serializers.models.Sum('store_amount'))['total'] or 0
        
        if validated_data['amount'] > pending_commissions:
            raise serializers.ValidationError(f"المبلغ المطلوب أكبر من المتوفر. المتوفر: {pending_commissions}")
        
        payout = Payout.objects.create(
            store=store,
            **validated_data
        )
        
        return payout
