from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from .models import StoreCategory, Store


@admin.register(StoreCategory)
class StoreCategoryAdmin(admin.ModelAdmin):
    list_display = ('name', 'slug', 'is_active', 'created_at')
    list_filter = ('is_active', 'created_at')
    search_fields = ('name', 'description')
    prepopulated_fields = {'slug': ('name',)}
    ordering = ('name',)


@admin.register(Store)
class StoreAdmin(admin.ModelAdmin):
    list_display = ('name', 'owner', 'category', 'status', 'commission_rate', 'is_featured', 'rating', 'created_at')
    list_filter = ('status', 'category', 'is_featured', 'created_at')
    search_fields = ('name', 'owner__username', 'owner__email', 'description', 'address')
    prepopulated_fields = {'slug': ('name',)}
    ordering = ('-created_at',)
    readonly_fields = ('total_sales', 'rating', 'created_at', 'updated_at')

    fieldsets = (
        (_('معلومات أساسية'), {
            'fields': ('owner', 'name', 'slug', 'description', 'category')
        }),
        (_('الصور'), {
            'fields': ('logo', 'banner')
        }),
        (_('معلومات الاتصال'), {
            'fields': ('address', 'phone', 'email', 'website')
        }),
        (_('إعدادات المتجر'), {
            'fields': ('status', 'commission_rate', 'is_featured')
        }),
        (_('إحصائيات'), {
            'fields': ('rating', 'total_sales', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('owner', 'category')
