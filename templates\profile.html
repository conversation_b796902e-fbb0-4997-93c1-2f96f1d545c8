<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الملف الشخصي - السوق الإلكتروني</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

        body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        .navbar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.8rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .profile-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 25px;
            position: relative;
            overflow: hidden;
        }
        .profile-header::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
            animation: float 20s infinite linear;
        }
        @keyframes float {
            0% { transform: translateX(0) translateY(0); }
            100% { transform: translateX(-50px) translateY(-50px); }
        }
        .profile-avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            border: 4px solid white;
            object-fit: cover;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }
        .profile-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s ease;
        }
        .profile-card:hover {
            transform: translateY(-2px);
        }
        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 15px;
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #007bff;
        }
        .form-floating label {
            right: 12px;
            left: auto;
        }
        .nav-pills .nav-link {
            border-radius: 25px;
            margin-left: 5px;
        }
        .nav-pills .nav-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .activity-item {
            display: flex;
            align-items: center;
            padding: 15px;
            border-bottom: 1px solid #eee;
        }
        .activity-item:last-child {
            border-bottom: none;
        }
        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 15px;
        }
        .badge-custom {
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.8rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-store"></i> السوق الإلكتروني
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/products/">المنتجات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/stores/">المتاجر</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle active" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> حسابي
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item active" href="/profile/">الملف الشخصي</a></li>
                            <li><a class="dropdown-item" href="/orders/">طلباتي</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/logout/">تسجيل الخروج</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Profile Header -->
        <div class="profile-header">
            <div class="row align-items-center">
                <div class="col-md-3 text-center">
                    <img src="/static/images/default-avatar.jpg" class="profile-avatar" alt="Profile Picture" id="profileImage">
                    <div class="mt-3">
                        <button class="btn btn-light btn-sm" onclick="document.getElementById('avatarInput').click()">
                            <i class="fas fa-camera"></i> تغيير الصورة
                        </button>
                        <input type="file" id="avatarInput" accept="image/*" style="display: none;" onchange="previewAvatar(event)">
                    </div>
                </div>
                <div class="col-md-6">
                    <h2 class="mb-2">أحمد محمد العلي</h2>
                    <p class="mb-2">
                        <i class="fas fa-envelope"></i> <EMAIL>
                    </p>
                    <p class="mb-2">
                        <i class="fas fa-phone"></i> +966 50 123 4567
                    </p>
                    <div class="mt-3">
                        <span class="badge badge-custom bg-success">عميل مُفعل</span>
                        <span class="badge badge-custom bg-primary">عضو منذ 2024</span>
                    </div>
                </div>
                <div class="col-md-3 text-center">
                    <div class="stat-card">
                        <div class="stat-number">12</div>
                        <div>إجمالي الطلبات</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-3">
                <!-- Navigation Tabs -->
                <div class="profile-card">
                    <ul class="nav nav-pills flex-column" id="profileTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active w-100 text-start" id="info-tab" data-bs-toggle="pill" 
                                    data-bs-target="#info" type="button" role="tab">
                                <i class="fas fa-user me-2"></i> المعلومات الشخصية
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link w-100 text-start" id="orders-tab" data-bs-toggle="pill" 
                                    data-bs-target="#orders" type="button" role="tab">
                                <i class="fas fa-shopping-bag me-2"></i> طلباتي
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link w-100 text-start" id="addresses-tab" data-bs-toggle="pill" 
                                    data-bs-target="#addresses" type="button" role="tab">
                                <i class="fas fa-map-marker-alt me-2"></i> العناوين
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link w-100 text-start" id="security-tab" data-bs-toggle="pill" 
                                    data-bs-target="#security" type="button" role="tab">
                                <i class="fas fa-shield-alt me-2"></i> الأمان
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link w-100 text-start" id="store-tab" data-bs-toggle="pill" 
                                    data-bs-target="#store" type="button" role="tab">
                                <i class="fas fa-store me-2"></i> متجري
                            </button>
                        </li>
                    </ul>
                </div>

                <!-- Quick Stats -->
                <div class="profile-card">
                    <h6 class="mb-3">إحصائيات سريعة</h6>
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="stat-number text-success">5</div>
                            <small>طلبات مكتملة</small>
                        </div>
                        <div class="col-6">
                            <div class="stat-number text-primary">2</div>
                            <small>طلبات قيد التنفيذ</small>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-9">
                <div class="tab-content" id="profileTabsContent">
                    <!-- Personal Information -->
                    <div class="tab-pane fade show active" id="info" role="tabpanel">
                        <div class="profile-card">
                            <h4 class="mb-4">
                                <i class="fas fa-user text-primary"></i> المعلومات الشخصية
                            </h4>
                            
                            <form id="profileForm">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-floating mb-3">
                                            <input type="text" class="form-control" id="firstName" value="أحمد">
                                            <label for="firstName">الاسم الأول</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-floating mb-3">
                                            <input type="text" class="form-control" id="lastName" value="محمد العلي">
                                            <label for="lastName">اسم العائلة</label>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-floating mb-3">
                                            <input type="email" class="form-control" id="email" value="<EMAIL>">
                                            <label for="email">البريد الإلكتروني</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-floating mb-3">
                                            <input type="tel" class="form-control" id="phone" value="+966501234567">
                                            <label for="phone">رقم الهاتف</label>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="form-floating mb-3">
                                    <input type="date" class="form-control" id="birthDate" value="1990-01-01">
                                    <label for="birthDate">تاريخ الميلاد</label>
                                </div>
                                
                                <div class="form-floating mb-3">
                                    <select class="form-select" id="gender">
                                        <option value="male" selected>ذكر</option>
                                        <option value="female">أنثى</option>
                                    </select>
                                    <label for="gender">الجنس</label>
                                </div>
                                
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> حفظ التغييرات
                                </button>
                            </form>
                        </div>
                    </div>

                    <!-- Orders -->
                    <div class="tab-pane fade" id="orders" role="tabpanel">
                        <div class="profile-card">
                            <div class="d-flex justify-content-between align-items-center mb-4">
                                <h4>
                                    <i class="fas fa-shopping-bag text-primary"></i> طلباتي الأخيرة
                                </h4>
                                <a href="/orders/" class="btn btn-outline-primary">
                                    عرض جميع الطلبات
                                </a>
                            </div>
                            
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>رقم الطلب</th>
                                            <th>التاريخ</th>
                                            <th>الحالة</th>
                                            <th>المبلغ</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>#1234567890</td>
                                            <td>2024-01-15</td>
                                            <td><span class="badge bg-success">تم التسليم</span></td>
                                            <td>9,298.00 ر.س</td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-primary">عرض</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>#1234567891</td>
                                            <td>2024-01-20</td>
                                            <td><span class="badge bg-warning">قيد المعالجة</span></td>
                                            <td>899.00 ر.س</td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-primary">عرض</button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Addresses -->
                    <div class="tab-pane fade" id="addresses" role="tabpanel">
                        <div class="profile-card">
                            <div class="d-flex justify-content-between align-items-center mb-4">
                                <h4>
                                    <i class="fas fa-map-marker-alt text-primary"></i> عناويني
                                </h4>
                                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addAddressModal">
                                    <i class="fas fa-plus"></i> إضافة عنوان
                                </button>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card mb-3">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between">
                                                <h6>المنزل</h6>
                                                <span class="badge bg-primary">افتراضي</span>
                                            </div>
                                            <p class="text-muted mb-2">
                                                شارع الملك فهد، حي العليا<br>
                                                الرياض 12345<br>
                                                المملكة العربية السعودية
                                            </p>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary">تعديل</button>
                                                <button class="btn btn-outline-danger">حذف</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Security -->
                    <div class="tab-pane fade" id="security" role="tabpanel">
                        <div class="profile-card">
                            <h4 class="mb-4">
                                <i class="fas fa-shield-alt text-primary"></i> الأمان والخصوصية
                            </h4>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>تغيير كلمة المرور</h6>
                                    <form>
                                        <div class="form-floating mb-3">
                                            <input type="password" class="form-control" id="currentPassword">
                                            <label for="currentPassword">كلمة المرور الحالية</label>
                                        </div>
                                        <div class="form-floating mb-3">
                                            <input type="password" class="form-control" id="newPassword">
                                            <label for="newPassword">كلمة المرور الجديدة</label>
                                        </div>
                                        <div class="form-floating mb-3">
                                            <input type="password" class="form-control" id="confirmPassword">
                                            <label for="confirmPassword">تأكيد كلمة المرور</label>
                                        </div>
                                        <button type="submit" class="btn btn-warning">
                                            <i class="fas fa-key"></i> تغيير كلمة المرور
                                        </button>
                                    </form>
                                </div>
                                <div class="col-md-6">
                                    <h6>إعدادات الأمان</h6>
                                    <div class="form-check form-switch mb-3">
                                        <input class="form-check-input" type="checkbox" id="twoFactor">
                                        <label class="form-check-label" for="twoFactor">
                                            تفعيل المصادقة الثنائية
                                        </label>
                                    </div>
                                    <div class="form-check form-switch mb-3">
                                        <input class="form-check-input" type="checkbox" id="emailNotifications" checked>
                                        <label class="form-check-label" for="emailNotifications">
                                            إشعارات البريد الإلكتروني
                                        </label>
                                    </div>
                                    <div class="form-check form-switch mb-3">
                                        <input class="form-check-input" type="checkbox" id="smsNotifications">
                                        <label class="form-check-label" for="smsNotifications">
                                            إشعارات الرسائل النصية
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Store Management -->
                    <div class="tab-pane fade" id="store" role="tabpanel">
                        <div class="profile-card">
                            <h4 class="mb-4">
                                <i class="fas fa-store text-primary"></i> إدارة المتجر
                            </h4>
                            
                            <div class="text-center py-5">
                                <i class="fas fa-store fa-5x text-muted mb-4"></i>
                                <h5>ليس لديك متجر بعد</h5>
                                <p class="text-muted mb-4">ابدأ رحلتك في البيع الإلكتروني وأنشئ متجرك الآن</p>
                                <a href="/add-store/" class="btn btn-success btn-lg">
                                    <i class="fas fa-plus"></i> إنشاء متجر جديد
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Address Modal -->
    <div class="modal fade" id="addAddressModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة عنوان جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form>
                        <div class="form-floating mb-3">
                            <input type="text" class="form-control" id="addressTitle">
                            <label for="addressTitle">عنوان العنوان (مثل: المنزل، العمل)</label>
                        </div>
                        <div class="form-floating mb-3">
                            <textarea class="form-control" id="fullAddress" style="height: 100px"></textarea>
                            <label for="fullAddress">العنوان الكامل</label>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" id="city">
                                    <label for="city">المدينة</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" id="postalCode">
                                    <label for="postalCode">الرمز البريدي</label>
                                </div>
                            </div>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="setDefault">
                            <label class="form-check-label" for="setDefault">
                                جعل هذا العنوان افتراضي
                            </label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary">حفظ العنوان</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container text-center">
            <p class="mb-0">&copy; 2024 السوق الإلكتروني. جميع الحقوق محفوظة.</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function previewAvatar(event) {
            const file = event.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('profileImage').src = e.target.result;
                };
                reader.readAsDataURL(file);
            }
        }

        // Form submissions
        document.getElementById('profileForm').addEventListener('submit', function(e) {
            e.preventDefault();
            alert('تم حفظ التغييرات بنجاح!');
        });

        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    </script>
</body>
</html>
