# Generated by Django 5.2.4 on 2025-07-16 23:02

import django.core.validators
import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('products', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Cart',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('session_key', models.CharField(blank=True, max_length=40, null=True, verbose_name='مفتاح الجلسة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='carts', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'سلة المشتريات',
                'verbose_name_plural': 'سلال المشتريات',
            },
        ),
        migrations.CreateModel(
            name='Order',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('order_number', models.CharField(max_length=20, unique=True, verbose_name='رقم الطلب')),
                ('status', models.CharField(choices=[('pending', 'في انتظار الدفع'), ('paid', 'مدفوع'), ('processing', 'قيد المعالجة'), ('shipped', 'تم الشحن'), ('delivered', 'تم التسليم'), ('cancelled', 'ملغي'), ('refunded', 'مُسترد')], default='pending', max_length=20, verbose_name='حالة الطلب')),
                ('billing_first_name', models.CharField(max_length=50, verbose_name='الاسم الأول للفاتورة')),
                ('billing_last_name', models.CharField(max_length=50, verbose_name='اسم العائلة للفاتورة')),
                ('billing_email', models.EmailField(max_length=254, verbose_name='البريد الإلكتروني للفاتورة')),
                ('billing_phone', models.CharField(max_length=20, verbose_name='رقم الهاتف للفاتورة')),
                ('billing_address', models.TextField(verbose_name='عنوان الفاتورة')),
                ('billing_city', models.CharField(max_length=100, verbose_name='مدينة الفاتورة')),
                ('billing_postal_code', models.CharField(max_length=20, verbose_name='الرمز البريدي للفاتورة')),
                ('billing_country', models.CharField(max_length=100, verbose_name='بلد الفاتورة')),
                ('shipping_first_name', models.CharField(max_length=50, verbose_name='الاسم الأول للشحن')),
                ('shipping_last_name', models.CharField(max_length=50, verbose_name='اسم العائلة للشحن')),
                ('shipping_phone', models.CharField(max_length=20, verbose_name='رقم الهاتف للشحن')),
                ('shipping_address', models.TextField(verbose_name='عنوان الشحن')),
                ('shipping_city', models.CharField(max_length=100, verbose_name='مدينة الشحن')),
                ('shipping_postal_code', models.CharField(max_length=20, verbose_name='الرمز البريدي للشحن')),
                ('shipping_country', models.CharField(max_length=100, verbose_name='بلد الشحن')),
                ('subtotal', models.DecimalField(decimal_places=2, max_digits=12, verbose_name='المجموع الفرعي')),
                ('shipping_cost', models.DecimalField(decimal_places=2, default=0.0, max_digits=10, verbose_name='تكلفة الشحن')),
                ('tax_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=10, verbose_name='مبلغ الضريبة')),
                ('total_commission', models.DecimalField(decimal_places=2, max_digits=12, verbose_name='إجمالي العمولة')),
                ('total_amount', models.DecimalField(decimal_places=2, max_digits=12, verbose_name='المبلغ الإجمالي')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('tracking_number', models.CharField(blank=True, max_length=100, null=True, verbose_name='رقم التتبع')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('shipped_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الشحن')),
                ('delivered_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ التسليم')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='orders', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'طلب',
                'verbose_name_plural': 'الطلبات',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='OrderItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.PositiveIntegerField(validators=[django.core.validators.MinValueValidator(1)], verbose_name='الكمية')),
                ('price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='السعر')),
                ('commission_rate', models.DecimalField(decimal_places=4, max_digits=5, verbose_name='معدل العمولة')),
                ('commission_amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='مبلغ العمولة')),
                ('store_name', models.CharField(max_length=200, verbose_name='اسم المتجر')),
                ('product_name', models.CharField(max_length=200, verbose_name='اسم المنتج')),
                ('product_sku', models.CharField(max_length=100, verbose_name='رمز المنتج')),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='orders.order', verbose_name='الطلب')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='products.product', verbose_name='المنتج')),
                ('variant', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='products.productvariant', verbose_name='متغير المنتج')),
            ],
            options={
                'verbose_name': 'عنصر الطلب',
                'verbose_name_plural': 'عناصر الطلب',
            },
        ),
        migrations.CreateModel(
            name='OrderStatusHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('pending', 'في انتظار الدفع'), ('paid', 'مدفوع'), ('processing', 'قيد المعالجة'), ('shipped', 'تم الشحن'), ('delivered', 'تم التسليم'), ('cancelled', 'ملغي'), ('refunded', 'مُسترد')], max_length=20, verbose_name='الحالة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ التغيير')),
                ('changed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='تم التغيير بواسطة')),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='status_history', to='orders.order', verbose_name='الطلب')),
            ],
            options={
                'verbose_name': 'تاريخ حالة الطلب',
                'verbose_name_plural': 'تاريخ حالات الطلبات',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='CartItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.PositiveIntegerField(default=1, validators=[django.core.validators.MinValueValidator(1)], verbose_name='الكمية')),
                ('price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='السعر')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإضافة')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('cart', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='orders.cart', verbose_name='السلة')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='products.product', verbose_name='المنتج')),
                ('variant', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='products.productvariant', verbose_name='متغير المنتج')),
            ],
            options={
                'verbose_name': 'عنصر السلة',
                'verbose_name_plural': 'عناصر السلة',
                'unique_together': {('cart', 'product', 'variant')},
            },
        ),
    ]
