<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إتمام الطلب - السوق الإلكتروني</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .checkout-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
        }
        .step {
            display: flex;
            align-items: center;
            margin: 0 15px;
        }
        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            color: #6c757d;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-left: 10px;
        }
        .step.active .step-number {
            background: #007bff;
            color: white;
        }
        .step.completed .step-number {
            background: #28a745;
            color: white;
        }
        .payment-method {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .payment-method:hover {
            border-color: #007bff;
        }
        .payment-method.selected {
            border-color: #007bff;
            background-color: #f8f9ff;
        }
        .order-summary {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            position: sticky;
            top: 20px;
        }
        .product-item {
            display: flex;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        .product-item:last-child {
            border-bottom: none;
        }
        .product-image {
            width: 50px;
            height: 50px;
            object-fit: cover;
            border-radius: 8px;
            margin-left: 15px;
        }
        .security-badges {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 20px;
        }
        .form-floating label {
            right: 12px;
            left: auto;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-store"></i> السوق الإلكتروني
            </a>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Step Indicator -->
        <div class="step-indicator">
            <div class="step completed">
                <div class="step-number">1</div>
                <span>السلة</span>
            </div>
            <div class="step active">
                <div class="step-number">2</div>
                <span>معلومات الشحن</span>
            </div>
            <div class="step">
                <div class="step-number">3</div>
                <span>الدفع</span>
            </div>
            <div class="step">
                <div class="step-number">4</div>
                <span>التأكيد</span>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-8">
                <!-- Shipping Information -->
                <div class="checkout-section" id="shippingSection">
                    <h4 class="mb-4">
                        <i class="fas fa-truck"></i> معلومات الشحن
                    </h4>
                    
                    <form id="shippingForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" id="firstName" required>
                                    <label for="firstName">الاسم الأول *</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" id="lastName" required>
                                    <label for="lastName">اسم العائلة *</label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="email" class="form-control" id="email" required>
                                    <label for="email">البريد الإلكتروني *</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="tel" class="form-control" id="phone" required>
                                    <label for="phone">رقم الهاتف *</label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-floating mb-3">
                            <textarea class="form-control" id="address" style="height: 100px" required></textarea>
                            <label for="address">العنوان الكامل *</label>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" id="city" required>
                                    <label for="city">المدينة *</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" id="postalCode">
                                    <label for="postalCode">الرمز البريدي</label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="sameAsBilling" checked>
                            <label class="form-check-label" for="sameAsBilling">
                                عنوان الفاتورة نفس عنوان الشحن
                            </label>
                        </div>
                        
                        <div class="form-floating mb-3">
                            <textarea class="form-control" id="notes" style="height: 80px"></textarea>
                            <label for="notes">ملاحظات إضافية (اختياري)</label>
                        </div>
                    </form>
                </div>

                <!-- Payment Methods -->
                <div class="checkout-section" id="paymentSection" style="display: none;">
                    <h4 class="mb-4">
                        <i class="fas fa-credit-card"></i> طريقة الدفع
                    </h4>
                    
                    <div class="payment-method" data-method="stripe">
                        <div class="d-flex align-items-center">
                            <input type="radio" name="paymentMethod" value="stripe" id="stripe" class="me-3">
                            <div class="flex-grow-1">
                                <h6 class="mb-1">بطاقة ائتمان / خصم</h6>
                                <small class="text-muted">Visa, Mastercard, American Express</small>
                            </div>
                            <div class="payment-icons">
                                <i class="fab fa-cc-visa fa-2x text-primary me-2"></i>
                                <i class="fab fa-cc-mastercard fa-2x text-warning me-2"></i>
                                <i class="fab fa-cc-amex fa-2x text-info"></i>
                            </div>
                        </div>
                        
                        <div id="stripeForm" style="display: none; margin-top: 20px;">
                            <div class="row">
                                <div class="col-12">
                                    <div class="form-floating mb-3">
                                        <input type="text" class="form-control" id="cardNumber" placeholder="1234 5678 9012 3456">
                                        <label for="cardNumber">رقم البطاقة</label>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-floating mb-3">
                                        <input type="text" class="form-control" id="expiryDate" placeholder="MM/YY">
                                        <label for="expiryDate">تاريخ الانتهاء</label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-floating mb-3">
                                        <input type="text" class="form-control" id="cvv" placeholder="123">
                                        <label for="cvv">رمز الأمان</label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="cardName">
                                <label for="cardName">اسم حامل البطاقة</label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="payment-method" data-method="paypal">
                        <div class="d-flex align-items-center">
                            <input type="radio" name="paymentMethod" value="paypal" id="paypal" class="me-3">
                            <div class="flex-grow-1">
                                <h6 class="mb-1">PayPal</h6>
                                <small class="text-muted">ادفع بأمان باستخدام حساب PayPal</small>
                            </div>
                            <i class="fab fa-paypal fa-3x text-primary"></i>
                        </div>
                    </div>
                    
                    <div class="payment-method" data-method="cod">
                        <div class="d-flex align-items-center">
                            <input type="radio" name="paymentMethod" value="cod" id="cod" class="me-3">
                            <div class="flex-grow-1">
                                <h6 class="mb-1">الدفع عند الاستلام</h6>
                                <small class="text-muted">ادفع نقداً عند استلام الطلب</small>
                            </div>
                            <i class="fas fa-money-bill-wave fa-2x text-success"></i>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4">
                <!-- Order Summary -->
                <div class="order-summary">
                    <h5 class="mb-3">ملخص الطلب</h5>
                    
                    <div id="orderItems">
                        <div class="product-item">
                            <img src="/static/images/iphone15.jpg" class="product-image" alt="iPhone 15 Pro">
                            <div class="flex-grow-1">
                                <h6 class="mb-1">iPhone 15 Pro</h6>
                                <small class="text-muted">متجر التقنية الحديثة</small>
                                <div class="d-flex justify-content-between">
                                    <span>الكمية: 1</span>
                                    <strong>4,999.00 ر.س</strong>
                                </div>
                            </div>
                        </div>
                        
                        <div class="product-item">
                            <img src="/static/images/samsung-s24.jpg" class="product-image" alt="Samsung Galaxy S24 Ultra">
                            <div class="flex-grow-1">
                                <h6 class="mb-1">Samsung Galaxy S24 Ultra</h6>
                                <small class="text-muted">متجر التقنية الحديثة</small>
                                <div class="d-flex justify-content-between">
                                    <span>الكمية: 1</span>
                                    <strong>4,299.00 ر.س</strong>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="d-flex justify-content-between mb-2">
                        <span>المجموع الفرعي:</span>
                        <span>9,298.00 ر.س</span>
                    </div>
                    
                    <div class="d-flex justify-content-between mb-2">
                        <span>الشحن:</span>
                        <span class="text-success">مجاني</span>
                    </div>
                    
                    <div class="d-flex justify-content-between mb-2">
                        <span>الضريبة:</span>
                        <span>0.00 ر.س</span>
                    </div>
                    
                    <hr>
                    
                    <div class="d-flex justify-content-between mb-3">
                        <strong>الإجمالي:</strong>
                        <strong class="text-primary fs-5">9,298.00 ر.س</strong>
                    </div>
                    
                    <button class="btn btn-primary w-100 btn-lg mb-3" id="continueBtn" onclick="nextStep()">
                        متابعة إلى الدفع
                    </button>
                    
                    <button class="btn btn-success w-100 btn-lg mb-3" id="placeOrderBtn" style="display: none;" onclick="placeOrder()">
                        <i class="fas fa-lock"></i> تأكيد الطلب والدفع
                    </button>
                    
                    <div class="text-center">
                        <small class="text-muted">
                            <i class="fas fa-shield-alt"></i>
                            معاملة آمنة ومشفرة بتقنية SSL
                        </small>
                    </div>
                    
                    <div class="security-badges">
                        <i class="fas fa-lock fa-2x text-success"></i>
                        <i class="fab fa-cc-visa fa-2x text-primary"></i>
                        <i class="fab fa-cc-mastercard fa-2x text-warning"></i>
                        <i class="fab fa-paypal fa-2x text-info"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container text-center">
            <p class="mb-0">&copy; 2024 السوق الإلكتروني. جميع الحقوق محفوظة.</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentStep = 1;

        function nextStep() {
            if (currentStep === 1) {
                // Validate shipping form
                const form = document.getElementById('shippingForm');
                if (!form.checkValidity()) {
                    form.reportValidity();
                    return;
                }
                
                // Move to payment step
                currentStep = 2;
                updateStepIndicator();
                document.getElementById('shippingSection').style.display = 'none';
                document.getElementById('paymentSection').style.display = 'block';
                document.getElementById('continueBtn').style.display = 'none';
                document.getElementById('placeOrderBtn').style.display = 'block';
            }
        }

        function updateStepIndicator() {
            const steps = document.querySelectorAll('.step');
            steps.forEach((step, index) => {
                step.classList.remove('active', 'completed');
                if (index + 1 < currentStep) {
                    step.classList.add('completed');
                } else if (index + 1 === currentStep) {
                    step.classList.add('active');
                }
            });
        }

        function placeOrder() {
            const selectedPayment = document.querySelector('input[name="paymentMethod"]:checked');
            if (!selectedPayment) {
                alert('يرجى اختيار طريقة الدفع');
                return;
            }

            // Show loading
            const btn = document.getElementById('placeOrderBtn');
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري المعالجة...';
            btn.disabled = true;

            // Simulate API call
            setTimeout(() => {
                if (selectedPayment.value === 'stripe') {
                    processStripePayment();
                } else if (selectedPayment.value === 'paypal') {
                    processPayPalPayment();
                } else if (selectedPayment.value === 'cod') {
                    processCODOrder();
                }
            }, 2000);
        }

        function processStripePayment() {
            // Simulate Stripe payment processing
            alert('تم الدفع بنجاح! سيتم إعادة توجيهك لصفحة التأكيد.');
            window.location.href = '/order-confirmation/';
        }

        function processPayPalPayment() {
            // Simulate PayPal payment processing
            alert('سيتم إعادة توجيهك إلى PayPal لإتمام الدفع.');
            window.location.href = '/order-confirmation/';
        }

        function processCODOrder() {
            // Process cash on delivery order
            alert('تم تأكيد طلبك! سيتم التواصل معك لتأكيد التسليم.');
            window.location.href = '/order-confirmation/';
        }

        // Payment method selection
        document.querySelectorAll('.payment-method').forEach(method => {
            method.addEventListener('click', function() {
                // Remove selected class from all methods
                document.querySelectorAll('.payment-method').forEach(m => m.classList.remove('selected'));
                
                // Add selected class to clicked method
                this.classList.add('selected');
                
                // Check the radio button
                const radio = this.querySelector('input[type="radio"]');
                radio.checked = true;
                
                // Show/hide stripe form
                const stripeForm = document.getElementById('stripeForm');
                if (radio.value === 'stripe') {
                    stripeForm.style.display = 'block';
                } else {
                    stripeForm.style.display = 'none';
                }
            });
        });

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateStepIndicator();
        });
    </script>
</body>
</html>
