from django.urls import path
from . import views

app_name = 'orders'

urlpatterns = [
    # Cart
    path('cart/', views.CartView.as_view(), name='cart-detail'),
    path('cart/add/', views.add_to_cart, name='add-to-cart'),
    path('cart/items/<int:item_id>/update/', views.update_cart_item, name='update-cart-item'),
    path('cart/items/<int:item_id>/remove/', views.remove_from_cart, name='remove-from-cart'),
    path('cart/clear/', views.clear_cart, name='clear-cart'),
    
    # Orders
    path('', views.OrderListView.as_view(), name='order-list'),
    path('create/', views.OrderCreateView.as_view(), name='order-create'),
    path('<uuid:pk>/', views.OrderDetailView.as_view(), name='order-detail'),
    path('<uuid:order_id>/update-status/', views.update_order_status, name='update-order-status'),
    path('<uuid:order_id>/cancel/', views.cancel_order, name='cancel-order'),

    # Vendor orders
    path('vendor/', views.vendor_orders, name='vendor-orders'),

    # Statistics
    path('statistics/', views.order_statistics, name='order-statistics'),
]
