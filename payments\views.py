from django.shortcuts import render, get_object_or_404
from rest_framework import generics, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.conf import settings
from django.db import transaction
from django.db.models import Sum
from .models import Payment, Commission, Payout
from .serializers import (
    PaymentSerializer, PaymentCreateSerializer, CommissionSerializer,
    PayoutSerializer, PayoutCreateSerializer
)
from orders.models import Order, OrderStatusHistory
import stripe

# Configure Stripe
stripe.api_key = settings.STRIPE_SECRET_KEY


class PaymentCreateView(generics.CreateAPIView):
    """
    إنشاء دفعة جديدة
    """
    serializer_class = PaymentCreateSerializer
    permission_classes = [IsAuthenticated]


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def process_stripe_payment(request):
    """
    معالجة الدفع عبر Stripe
    """
    payment_id = request.data.get('payment_id')
    payment_method_id = request.data.get('payment_method_id')

    try:
        payment = Payment.objects.get(
            id=payment_id,
            user=request.user,
            status='pending'
        )
    except Payment.DoesNotExist:
        return Response({'error': 'الدفعة غير موجودة'},
                      status=status.HTTP_404_NOT_FOUND)

    try:
        with transaction.atomic():
            # Create Stripe PaymentIntent
            intent = stripe.PaymentIntent.create(
                amount=int(payment.amount * 100),  # Convert to cents
                currency=payment.currency.lower(),
                payment_method=payment_method_id,
                confirmation_method='manual',
                confirm=True,
                metadata={
                    'payment_id': str(payment.id),
                    'order_id': str(payment.order.id),
                    'user_id': str(payment.user.id)
                }
            )

            # Update payment with Stripe transaction ID
            payment.gateway_transaction_id = intent.id
            payment.gateway_response = intent

            if intent.status == 'succeeded':
                payment.status = 'completed'
                payment.save()

                # Update order status
                order = payment.order
                order.status = 'paid'
                order.save()

                # Create order status history
                OrderStatusHistory.objects.create(
                    order=order,
                    status='paid',
                    notes='تم الدفع بنجاح عبر Stripe',
                    changed_by=request.user
                )

                # Create commissions for each store
                create_commissions_for_order(order, payment)

                return Response({
                    'success': True,
                    'payment_id': payment.id,
                    'order_id': order.id,
                    'message': 'تم الدفع بنجاح'
                })

            elif intent.status == 'requires_action':
                payment.status = 'processing'
                payment.save()

                return Response({
                    'requires_action': True,
                    'client_secret': intent.client_secret
                })

            else:
                payment.status = 'failed'
                payment.failure_reason = f"Stripe status: {intent.status}"
                payment.save()

                return Response({'error': 'فشل في معالجة الدفع'},
                              status=status.HTTP_400_BAD_REQUEST)

    except stripe.error.StripeError as e:
        payment.status = 'failed'
        payment.failure_reason = str(e)
        payment.save()

        return Response({'error': 'خطأ في معالجة الدفع'},
                      status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        return Response({'error': 'خطأ غير متوقع'},
                      status=status.HTTP_500_INTERNAL_SERVER_ERROR)


def create_commissions_for_order(order, payment):
    """
    إنشاء العمولات لكل متجر في الطلب
    """
    # Group order items by store
    stores_data = {}
    for item in order.items.all():
        store = item.product.store
        if store.id not in stores_data:
            stores_data[store.id] = {
                'store': store,
                'total_amount': 0,
                'commission_amount': 0
            }

        stores_data[store.id]['total_amount'] += item.total_price
        stores_data[store.id]['commission_amount'] += item.commission_amount

    # Create commission records
    for store_data in stores_data.values():
        Commission.objects.create(
            order=order,
            store=store_data['store'],
            payment=payment,
            order_amount=store_data['total_amount'],
            commission_rate=store_data['store'].commission_rate,
            commission_amount=store_data['commission_amount'],
            store_amount=store_data['total_amount'] - store_data['commission_amount']
        )


class CommissionListView(generics.ListAPIView):
    """
    عرض قائمة العمولات (للمدراء)
    """
    serializer_class = CommissionSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        user = self.request.user
        if user.is_admin_user or user.is_staff:
            return Commission.objects.all().select_related('order', 'store', 'payment')
        elif user.is_vendor:
            # Show commissions for user's stores
            return Commission.objects.filter(
                store__owner=user
            ).select_related('order', 'store', 'payment')
        else:
            return Commission.objects.none()


class PayoutListView(generics.ListAPIView):
    """
    عرض قائمة الدفعات
    """
    serializer_class = PayoutSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        user = self.request.user
        if user.is_admin_user or user.is_staff:
            return Payout.objects.all().select_related('store')
        elif user.is_vendor:
            return Payout.objects.filter(
                store__owner=user
            ).select_related('store')
        else:
            return Payout.objects.none()


class PayoutCreateView(generics.CreateAPIView):
    """
    طلب دفعة جديدة (للمتاجر)
    """
    serializer_class = PayoutCreateSerializer
    permission_classes = [IsAuthenticated]


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def vendor_dashboard(request):
    """
    لوحة تحكم المتجر - إحصائيات العمولات والدفعات
    """
    user = request.user
    if not user.is_vendor:
        return Response({'error': 'غير مصرح'}, status=status.HTTP_403_FORBIDDEN)

    store = user.stores.filter(status='approved').first()
    if not store:
        return Response({'error': 'لا يوجد متجر مُوافق عليه'},
                      status=status.HTTP_404_NOT_FOUND)

    # Calculate statistics
    commissions = Commission.objects.filter(store=store)

    total_earnings = commissions.filter(status='paid').aggregate(
        total=Sum('store_amount')
    )['total'] or 0

    pending_earnings = commissions.filter(status='pending').aggregate(
        total=Sum('store_amount')
    )['total'] or 0

    total_commission_paid = commissions.filter(status='paid').aggregate(
        total=Sum('commission_amount')
    )['total'] or 0

    recent_commissions = CommissionSerializer(
        commissions.order_by('-created_at')[:10],
        many=True
    ).data

    recent_payouts = PayoutSerializer(
        Payout.objects.filter(store=store).order_by('-created_at')[:10],
        many=True
    ).data

    return Response({
        'store': {
            'id': store.id,
            'name': store.name,
            'commission_rate': store.commission_rate
        },
        'statistics': {
            'total_earnings': total_earnings,
            'pending_earnings': pending_earnings,
            'total_commission_paid': total_commission_paid
        },
        'recent_commissions': recent_commissions,
        'recent_payouts': recent_payouts
    })
