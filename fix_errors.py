#!/usr/bin/env python
"""
إصلاح الأخطاء الشائعة في المشروع
Fix common errors in the project
"""
import os
import sys

def install_missing_packages():
    """تثبيت المكتبات المفقودة"""
    print("📦 تثبيت المكتبات المطلوبة...")
    
    packages = [
        'django==5.2.4',
        'djangorestframework==3.16.0',
        'pillow==11.3.0',
        'python-decouple==3.8',
        'psycopg2-binary==2.9.10',
        'celery==5.5.3',
        'redis==6.2.0',
        'stripe==12.3.0'
    ]
    
    import subprocess
    
    for package in packages:
        try:
            print(f"تثبيت {package}...")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
            print(f"✅ تم تثبيت {package}")
        except subprocess.CalledProcessError as e:
            print(f"❌ فشل تثبيت {package}: {e}")

def create_env_file():
    """إنشاء ملف .env إذا لم يكن موجوداً"""
    if not os.path.exists('.env'):
        print("📄 إنشاء ملف .env...")
        env_content = """# Django settings
SECRET_KEY=django-insecure-fix-this-in-production
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# Database settings (for production)
DB_NAME=marketplace_db
DB_USER=marketplace_user
DB_PASSWORD=marketplace_password
DB_HOST=localhost
DB_PORT=5432

# Email settings
EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend
EMAIL_HOST=
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=
EMAIL_HOST_PASSWORD=

# Stripe settings
STRIPE_PUBLISHABLE_KEY=pk_test_your_publishable_key_here
STRIPE_SECRET_KEY=sk_test_your_secret_key_here

# Commission settings
DEFAULT_COMMISSION_RATE=0.05
"""
        with open('.env', 'w', encoding='utf-8') as f:
            f.write(env_content)
        print("✅ تم إنشاء ملف .env")
    else:
        print("✅ ملف .env موجود")

def check_django_setup():
    """فحص إعداد Django"""
    try:
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'marketplace.settings')
        import django
        django.setup()
        print("✅ Django يعمل بشكل صحيح")
        return True
    except Exception as e:
        print(f"❌ خطأ في Django: {e}")
        return False

def run_migrations():
    """تشغيل الهجرات"""
    try:
        import subprocess
        print("🔄 تشغيل الهجرات...")
        
        # Make migrations
        result = subprocess.run([sys.executable, 'manage.py', 'makemigrations'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ تم إنشاء ملفات الهجرة")
        else:
            print(f"⚠️  تحذير في إنشاء الهجرات: {result.stderr}")
        
        # Apply migrations
        result = subprocess.run([sys.executable, 'manage.py', 'migrate'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ تم تطبيق الهجرات")
        else:
            print(f"❌ خطأ في تطبيق الهجرات: {result.stderr}")
            
    except Exception as e:
        print(f"❌ خطأ في الهجرات: {e}")

def create_superuser():
    """إنشاء مستخدم إداري"""
    try:
        import subprocess
        print("👤 إنشاء مستخدم إداري...")
        
        result = subprocess.run([sys.executable, 'create_admin.py'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ تم إنشاء المستخدم الإداري")
            print("📋 بيانات الدخول: admin / admin123")
        else:
            print(f"⚠️  المستخدم الإداري موجود بالفعل أو حدث خطأ: {result.stderr}")
            
    except Exception as e:
        print(f"❌ خطأ في إنشاء المستخدم الإداري: {e}")

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    directories = ['static', 'media', 'templates']
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"✅ تم إنشاء مجلد {directory}")
        else:
            print(f"✅ مجلد {directory} موجود")

def test_imports():
    """اختبار الاستيرادات"""
    print("🧪 اختبار الاستيرادات...")
    
    imports_to_test = [
        ('django', 'Django'),
        ('rest_framework', 'Django REST Framework'),
        ('PIL', 'Pillow'),
        ('decouple', 'Python Decouple'),
        ('celery', 'Celery'),
        ('redis', 'Redis'),
        ('stripe', 'Stripe')
    ]
    
    failed_imports = []
    
    for module, name in imports_to_test:
        try:
            __import__(module)
            print(f"✅ {name}")
        except ImportError:
            print(f"❌ {name} - مفقود")
            failed_imports.append(module)
    
    if failed_imports:
        print(f"\n💡 لتثبيت المكتبات المفقودة:")
        print(f"pip install {' '.join(failed_imports)}")
        return False
    
    return True

def main():
    print("🔧 إصلاح أخطاء المشروع")
    print("=" * 40)
    
    # التحقق من Python
    if sys.version_info < (3, 8):
        print("❌ يتطلب Python 3.8 أو أحدث")
        return
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}")
    
    # إنشاء المجلدات
    create_directories()
    
    # إنشاء ملف .env
    create_env_file()
    
    # اختبار الاستيرادات
    if not test_imports():
        print("\n🔄 تثبيت المكتبات المفقودة...")
        install_missing_packages()
    
    # فحص Django
    if check_django_setup():
        # تشغيل الهجرات
        run_migrations()
        
        # إنشاء مستخدم إداري
        create_superuser()
    
    print("\n🎉 تم الانتهاء من الإصلاحات!")
    print("=" * 40)
    print("🚀 لتشغيل الخادم:")
    print("python manage.py runserver")
    print("\n🌐 الوصول للموقع:")
    print("http://127.0.0.1:8000")
    print("\n🔧 لوحة التحكم:")
    print("http://127.0.0.1:8000/admin/")
    print("المستخدم: admin")
    print("كلمة المرور: admin123")

if __name__ == "__main__":
    main()
