# السوق الإلكتروني - Marketplace Platform

منصة سوق إلكتروني شاملة تجمع بين المتاجر والعملاء مع نظام عمولات متقدم، مبنية باستخدام Django و Django REST Framework.

## 🚀 المميزات الرئيسية

### 📊 إدارة المستخدمين
- نظام مستخدمين متعدد الأنواع (عملاء، أصحاب متاجر، مدراء)
- تسجيل دخول وإنشاء حسابات آمن
- ملفات تعريف مخصصة لكل نوع مستخدم

### 🏪 إدارة المتاجر
- تسجيل المتاجر والموافقة عليها
- تصنيفات المتاجر
- رفع شعارات وبانرات المتاجر
- نظام تقييم المتاجر

### 📦 إدارة المنتجات
- إضافة وإدارة المنتجات
- صور متعددة للمنتجات
- متغيرات المنتجات (ألوان، أحجام، إلخ)
- إدارة المخزون
- تصنيفات هرمية للمنتجات
- نظام تقييم المنتجات

### 🛒 نظام التسوق
- سلة مشتريات ذكية
- حساب العمولات تلقائياً
- إدارة الكميات والمخزون
- حفظ المنتجات للمفضلة

### 💳 نظام الدفع والعمولات
- تكامل مع Stripe للدفع الإلكتروني
- حساب العمولات تلقائياً
- توزيع الأرباح بين المنصة والمتاجر
- نظام دفعات للمتاجر

### 📋 إدارة الطلبات
- تتبع الطلبات بحالات متعددة
- إشعارات للعملاء والمتاجر
- إدارة الشحن والتسليم
- تاريخ تغييرات حالة الطلب

### 📈 لوحات التحكم والتقارير
- لوحة تحكم شاملة للمدراء
- لوحة تحكم للمتاجر
- تقارير المبيعات والعمولات
- إحصائيات مفصلة

## 🛠️ التقنيات المستخدمة

- **Backend**: Django 5.2.4
- **API**: Django REST Framework 3.16.0
- **Database**: SQLite (قابل للتغيير إلى PostgreSQL)
- **Authentication**: Django Authentication + Token Authentication
- **Payment**: Stripe Integration
- **File Storage**: Local Storage (قابل للتغيير إلى AWS S3)
- **Task Queue**: Celery + Redis
- **Frontend**: Bootstrap 5 + JavaScript

## 📁 هيكل المشروع

```
marketplace/
├── accounts/          # إدارة المستخدمين
├── stores/           # إدارة المتاجر
├── products/         # إدارة المنتجات
├── orders/           # إدارة الطلبات وسلة المشتريات
├── payments/         # نظام الدفع والعمولات
├── dashboard/        # لوحات التحكم والتقارير
├── templates/        # قوالب HTML
├── static/           # ملفات CSS/JS/Images
├── media/            # ملفات المستخدمين المرفوعة
└── marketplace/      # إعدادات المشروع الرئيسية
```

## 🚀 التثبيت والتشغيل

### 1. إنشاء البيئة الافتراضية
```bash
python -m venv marketplace_env
source marketplace_env/Scripts/activate  # Windows
# أو
source marketplace_env/bin/activate      # Linux/Mac
```

### 2. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 3. إعداد قاعدة البيانات
```bash
python manage.py makemigrations
python manage.py migrate
```

### 4. إنشاء مستخدم إداري
```bash
python create_admin.py
```

### 5. تشغيل الخادم
```bash
python manage.py runserver
```

## 🔧 الإعدادات

### متغيرات البيئة (.env)
```env
SECRET_KEY=your-secret-key
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# إعدادات Stripe
STRIPE_PUBLISHABLE_KEY=pk_test_your_key
STRIPE_SECRET_KEY=sk_test_your_key

# إعدادات العمولة
DEFAULT_COMMISSION_RATE=0.05
```

## 📚 API Documentation

### المنتجات
- `GET /api/products/` - قائمة المنتجات
- `GET /api/products/{slug}/` - تفاصيل منتج
- `POST /api/products/create/` - إنشاء منتج (للمتاجر)
- `GET /api/products/featured/` - المنتجات المميزة
- `GET /api/products/search/` - البحث في المنتجات

### سلة المشتريات
- `GET /api/orders/cart/` - عرض السلة
- `POST /api/orders/cart/add/` - إضافة منتج للسلة
- `PUT /api/orders/cart/items/{id}/update/` - تحديث كمية
- `DELETE /api/orders/cart/items/{id}/remove/` - حذف من السلة

### الطلبات
- `GET /api/orders/` - قائمة الطلبات
- `POST /api/orders/create/` - إنشاء طلب جديد
- `GET /api/orders/{id}/` - تفاصيل طلب
- `POST /api/orders/{id}/cancel/` - إلغاء طلب

### المدفوعات
- `POST /api/payments/create/` - إنشاء دفعة
- `POST /api/payments/stripe/process/` - معالجة دفع Stripe
- `GET /api/payments/commissions/` - قائمة العمولات

### لوحات التحكم
- `GET /api/dashboard/admin/` - لوحة تحكم المدراء
- `GET /api/dashboard/vendor/` - لوحة تحكم المتاجر

## 👥 أنواع المستخدمين

### العملاء (Customers)
- تصفح المنتجات والمتاجر
- إضافة منتجات لسلة المشتريات
- إنشاء طلبات ودفع
- تتبع الطلبات
- تقييم المنتجات

### أصحاب المتاجر (Vendors)
- إدارة متاجرهم
- إضافة وإدارة المنتجات
- تتبع الطلبات والمبيعات
- إدارة المخزون
- طلب دفعات الأرباح

### المدراء (Admins)
- إدارة جميع المستخدمين والمتاجر
- الموافقة على المتاجر الجديدة
- مراقبة النظام والإحصائيات
- إدارة العمولات والدفعات

## 🔐 الأمان

- استخدام Django's built-in security features
- Token-based authentication للـ API
- CSRF protection
- SQL injection protection
- XSS protection

## 📱 الاستجابة والتوافق

- تصميم متجاوب يعمل على جميع الأجهزة
- متوافق مع جميع المتصفحات الحديثة
- دعم اللغة العربية (RTL)

## 🚀 التطوير المستقبلي

- [ ] تطبيق موبايل (React Native/Flutter)
- [ ] نظام إشعارات متقدم
- [ ] تكامل مع بوابات دفع إضافية
- [ ] نظام كوبونات وخصومات
- [ ] تحليلات متقدمة
- [ ] نظام مراجعات المتاجر
- [ ] دعم متعدد اللغات

## 📞 الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى إنشاء issue في المستودع.

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.
