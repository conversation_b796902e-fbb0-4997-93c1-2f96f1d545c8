<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>سلة المشتريات - السوق الإلكتروني</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

        body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        .navbar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.8rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 0 30px;
            margin-top: 76px;
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
            animation: float 20s infinite linear;
        }

        @keyframes float {
            0% { transform: translateX(0) translateY(0); }
            100% { transform: translateX(-50px) translateY(-50px); }
        }
        .cart-item {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .cart-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .cart-item:hover::before {
            transform: scaleX(1);
        }

        .cart-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
            background: rgba(255, 255, 255, 1);
        }
        .product-image {
            width: 100px;
            height: 100px;
            object-fit: cover;
            border-radius: 10px;
        }
        .quantity-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .quantity-btn {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            border: none;
            background: #007bff;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .quantity-input {
            width: 60px;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 5px;
        }
        .cart-summary {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 20px;
        }
        .price {
            font-size: 1.2rem;
            font-weight: bold;
            color: #28a745;
        }
        .total-price {
            font-size: 1.5rem;
            font-weight: bold;
            color: #007bff;
        }
        .empty-cart {
            text-align: center;
            padding: 60px 20px;
        }
        .commission-info {
            background: #e3f2fd;
            border-radius: 10px;
            padding: 15px;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-store"></i> السوق الإلكتروني
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/products/">المنتجات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/stores/">المتاجر</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link active" href="/cart/">
                            <i class="fas fa-shopping-cart"></i> السلة <span class="badge bg-primary" id="cartCount">2</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/login/">
                            <i class="fas fa-user"></i> تسجيل الدخول
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h2 class="mb-4">
                    <i class="fas fa-shopping-cart"></i> سلة المشتريات
                </h2>
            </div>
        </div>

        <div class="row" id="cartContent">
            <!-- Cart items will be loaded here -->
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container text-center">
            <p class="mb-0">&copy; 2024 السوق الإلكتروني. جميع الحقوق محفوظة.</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Sample cart data
        let cartData = {
            items: [
                {
                    id: 1,
                    product: {
                        id: 1,
                        name: 'iPhone 15 Pro',
                        image: '/static/images/iphone15.jpg',
                        store: { name: 'متجر التقنية الحديثة' }
                    },
                    quantity: 1,
                    price: 4999.00,
                    total_price: 4999.00,
                    commission_amount: 249.95
                },
                {
                    id: 2,
                    product: {
                        id: 2,
                        name: 'Samsung Galaxy S24 Ultra',
                        image: '/static/images/samsung-s24.jpg',
                        store: { name: 'متجر التقنية الحديثة' }
                    },
                    quantity: 1,
                    price: 4299.00,
                    total_price: 4299.00,
                    commission_amount: 214.95
                }
            ],
            subtotal: 9298.00,
            total_commission: 464.90,
            total_price: 9298.00
        };

        function loadCart() {
            const cartContent = document.getElementById('cartContent');
            
            if (cartData.items.length === 0) {
                cartContent.innerHTML = `
                    <div class="col-12">
                        <div class="empty-cart">
                            <i class="fas fa-shopping-cart fa-5x text-muted mb-4"></i>
                            <h3>سلة المشتريات فارغة</h3>
                            <p class="text-muted mb-4">لم تقم بإضافة أي منتجات إلى سلة المشتريات بعد</p>
                            <a href="/products/" class="btn btn-primary btn-lg">
                                <i class="fas fa-shopping-bag"></i> تصفح المنتجات
                            </a>
                        </div>
                    </div>
                `;
                return;
            }

            cartContent.innerHTML = `
                <div class="col-lg-8">
                    <div id="cartItems">
                        ${cartData.items.map(item => `
                            <div class="cart-item" data-item-id="${item.id}">
                                <div class="row align-items-center">
                                    <div class="col-md-2">
                                        <img src="${item.product.image || '/static/images/no-image.png'}" 
                                             class="product-image" alt="${item.product.name}">
                                    </div>
                                    <div class="col-md-4">
                                        <h6 class="mb-1">${item.product.name}</h6>
                                        <small class="text-muted">
                                            <i class="fas fa-store"></i> ${item.product.store.name}
                                        </small>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="quantity-controls">
                                            <button class="quantity-btn" onclick="updateQuantity(${item.id}, ${item.quantity - 1})">
                                                <i class="fas fa-minus"></i>
                                            </button>
                                            <input type="number" class="quantity-input" value="${item.quantity}" 
                                                   min="1" onchange="updateQuantity(${item.id}, this.value)">
                                            <button class="quantity-btn" onclick="updateQuantity(${item.id}, ${item.quantity + 1})">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="price">${item.price.toFixed(2)} ر.س</div>
                                        <small class="text-muted">للقطعة الواحدة</small>
                                    </div>
                                    <div class="col-md-1">
                                        <div class="price">${item.total_price.toFixed(2)} ر.س</div>
                                        <small class="text-muted">الإجمالي</small>
                                    </div>
                                    <div class="col-md-1">
                                        <button class="btn btn-outline-danger btn-sm" onclick="removeItem(${item.id})">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                    
                    <div class="mt-4">
                        <div class="row">
                            <div class="col-md-6">
                                <a href="/products/" class="btn btn-outline-primary">
                                    <i class="fas fa-arrow-right"></i> متابعة التسوق
                                </a>
                            </div>
                            <div class="col-md-6 text-end">
                                <button class="btn btn-outline-danger" onclick="clearCart()">
                                    <i class="fas fa-trash"></i> مسح السلة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4">
                    <div class="cart-summary">
                        <h5 class="mb-3">ملخص الطلب</h5>
                        
                        <div class="d-flex justify-content-between mb-2">
                            <span>المجموع الفرعي:</span>
                            <span>${cartData.subtotal.toFixed(2)} ر.س</span>
                        </div>
                        
                        <div class="d-flex justify-content-between mb-2">
                            <span>الشحن:</span>
                            <span class="text-success">مجاني</span>
                        </div>
                        
                        <div class="commission-info">
                            <small class="text-muted">
                                <i class="fas fa-info-circle"></i>
                                يتم خصم عمولة المنصة (${cartData.total_commission.toFixed(2)} ر.س) من المتاجر
                            </small>
                        </div>
                        
                        <hr>
                        
                        <div class="d-flex justify-content-between mb-3">
                            <strong>الإجمالي:</strong>
                            <strong class="total-price">${cartData.total_price.toFixed(2)} ر.س</strong>
                        </div>
                        
                        <button class="btn btn-success w-100 btn-lg mb-3" onclick="proceedToCheckout()">
                            <i class="fas fa-credit-card"></i> إتمام الطلب
                        </button>
                        
                        <div class="text-center">
                            <small class="text-muted">
                                <i class="fas fa-shield-alt"></i>
                                دفع آمن ومضمون
                            </small>
                        </div>
                        
                        <div class="mt-3">
                            <h6>طرق الدفع المقبولة:</h6>
                            <div class="d-flex gap-2 mt-2">
                                <i class="fab fa-cc-visa fa-2x text-primary"></i>
                                <i class="fab fa-cc-mastercard fa-2x text-warning"></i>
                                <i class="fab fa-cc-paypal fa-2x text-info"></i>
                                <i class="fas fa-money-bill-wave fa-2x text-success"></i>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function updateQuantity(itemId, newQuantity) {
            if (newQuantity < 1) {
                removeItem(itemId);
                return;
            }
            
            const item = cartData.items.find(item => item.id === itemId);
            if (item) {
                item.quantity = parseInt(newQuantity);
                item.total_price = item.price * item.quantity;
                item.commission_amount = item.total_price * 0.05; // 5% commission
                
                updateCartTotals();
                loadCart();
            }
        }

        function removeItem(itemId) {
            if (confirm('هل أنت متأكد من حذف هذا المنتج من السلة؟')) {
                cartData.items = cartData.items.filter(item => item.id !== itemId);
                updateCartTotals();
                updateCartCount();
                loadCart();
            }
        }

        function clearCart() {
            if (confirm('هل أنت متأكد من مسح جميع المنتجات من السلة؟')) {
                cartData.items = [];
                updateCartTotals();
                updateCartCount();
                loadCart();
            }
        }

        function updateCartTotals() {
            cartData.subtotal = cartData.items.reduce((sum, item) => sum + item.total_price, 0);
            cartData.total_commission = cartData.items.reduce((sum, item) => sum + item.commission_amount, 0);
            cartData.total_price = cartData.subtotal;
        }

        function updateCartCount() {
            const count = cartData.items.reduce((sum, item) => sum + item.quantity, 0);
            document.getElementById('cartCount').textContent = count;
        }

        function proceedToCheckout() {
            if (cartData.items.length === 0) {
                alert('السلة فارغة!');
                return;
            }
            
            // Redirect to checkout page
            window.location.href = '/checkout/';
        }

        // Load cart on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadCart();
            updateCartCount();
        });
    </script>
</body>
</html>
