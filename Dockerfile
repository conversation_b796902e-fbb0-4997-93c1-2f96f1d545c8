# استخدام Python 3.10 كصورة أساسية
FROM python:3.10-slim

# تعيين متغيرات البيئة
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV DJANGO_SETTINGS_MODULE=marketplace.settings

# إنشاء مجلد العمل
WORKDIR /app

# تثبيت متطلبات النظام
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        postgresql-client \
        build-essential \
        libpq-dev \
        gettext \
        curl \
    && rm -rf /var/lib/apt/lists/*

# نسخ ملف المتطلبات وتثبيتها
COPY requirements.txt /app/
RUN pip install --no-cache-dir -r requirements.txt

# نسخ ملفات المشروع
COPY . /app/

# إنشاء مجلدات الملفات الثابتة والوسائط
RUN mkdir -p /app/staticfiles /app/media

# جمع الملفات الثابتة
RUN python manage.py collectstatic --noinput

# إنشاء مستخدم غير جذر
RUN adduser --disabled-password --gecos '' appuser
RUN chown -R appuser:appuser /app
USER appuser

# فتح المنفذ
EXPOSE 8000

# أمر التشغيل الافتراضي
CMD ["gunicorn", "--bind", "0.0.0.0:8000", "marketplace.wsgi:application"]
